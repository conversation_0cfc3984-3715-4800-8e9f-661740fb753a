{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "CommitDraftTransactionRequest", "$protected": true, "title": "CommitDraftTransactionRequest", "description": "Commit Draft Transaction Request", "type": "object", "required": ["description", "payment_draft_identifier", "reference_id", "totp"], "properties": {"description": {"type": "string"}, "payment_draft_identifier": {"type": "string"}, "reference_id": {"type": "string"}, "totp": {"type": "string"}}}