{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "HealthCheckRequest", "$protected": true, "title": "HealthCheckRequest", "description": "Health check request", "type": "object", "required": ["source_account", "destination_account", "card_number", "amount", "asset", "description", "reference_id"], "properties": {"source_account": {"type": "string"}, "destination_account": {"type": "string"}, "card_number": {"type": "string"}, "amount": {"type": "number"}, "asset": {"type": "string"}, "description": {"type": "string"}, "reference_id": {"type": "string"}, "metadata": {"type": "object"}}}