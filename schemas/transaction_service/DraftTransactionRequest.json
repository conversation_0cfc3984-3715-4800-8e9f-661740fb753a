{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "DraftTransactionRequest", "$protected": true, "title": "DraftTransactionRequest", "description": "Draft Transaction Request", "type": "object", "required": ["amount", "asset", "card_number", "description", "destination_account", "draft_type", "metadata", "payer_identifier", "reference_id"], "properties": {"amount": {"type": "string"}, "asset": {"type": "string"}, "card_number": {"type": "string"}, "destination_account": {"type": "string"}, "draft_type": {"type": "string"}, "payer_identifier": {"type": "string"}, "reference_id": {"type": "string"}, "metadata": {"type": "object"}}}