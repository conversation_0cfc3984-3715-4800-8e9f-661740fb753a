{"swagger": "2.0", "info": {"title": "Transaction Service API", "description": "Transaction Service API for processing transaction request and related operations.", "version": "1.0.0", "contact": {"name": "Transaction Service API Support", "email": "<EMAIL>"}}, "tags": [{"name": "TransactionService"}], "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/transaction/commit": {"get": {"summary": "CommitTransaction commit transaction to Core Payment Service", "operationId": "TransactionService_CommitTransaction", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbCommitTransactionResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "source_account", "description": "source_account is the source account address for payer of the payment", "in": "query", "required": false, "type": "string"}, {"name": "destination_account", "description": "destination_account is the destination account address for acceptor of the payment", "in": "query", "required": false, "type": "string"}, {"name": "card_number", "description": "card_number is the card number of the payer", "in": "query", "required": false, "type": "string"}, {"name": "amount", "description": "amount is the amount to be paid", "in": "query", "required": false, "type": "number", "format": "double"}, {"name": "asset", "description": "asset is the asset type of the amount", "in": "query", "required": false, "type": "string"}, {"name": "description", "description": "description is the description of the payment", "in": "query", "required": false, "type": "string"}, {"name": "reference_id", "description": "reference_id is actually idempotency key for making create draft transaction idempotent", "in": "query", "required": false, "type": "string"}, {"name": "metadata[string]", "description": "metadata is the metadata of the transaction\n\nThis is a request variable of the map type. The query format is \"map_name[key]=value\", e.g. If the map name is Age, the key type is string, and the value type is integer, the query parameter is expressed as Age[\"bob\"]=18", "in": "query", "required": false, "type": "string"}], "tags": ["TransactionService"]}}, "/v1/transaction/draft/commit": {"get": {"summary": "CommitDraftTransaction validate TOTP code that sent to customer's mobile number and commit transaction to Core Payment Service", "operationId": "TransactionService_CommitDraftTransaction", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbCommitDraftTransactionResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "payment_draft_identifier", "description": "payment draft id", "in": "query", "required": false, "type": "string"}, {"name": "reference_id", "description": "reference_id is actually idempotency key for making create draft transaction idempotent", "in": "query", "required": false, "type": "string"}, {"name": "totp", "description": "totp code", "in": "query", "required": false, "type": "string"}, {"name": "description", "description": "description is the description of the payment", "in": "query", "required": false, "type": "string"}], "tags": ["TransactionService"]}}, "/v1/transaction/draft/draft": {"get": {"summary": "DraftTransaction process request for storing temporary transaction with specified customer mobile/card no or national id", "operationId": "TransactionService_DraftTransaction", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDraftTransactionResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "payer_identifier", "description": "payer_identifier is the mobile / national id / card no of the payer", "in": "query", "required": false, "type": "string"}, {"name": "draft_type", "description": "draft_type is the type of draft transaction", "in": "query", "required": false, "type": "string"}, {"name": "destination_account", "description": "destination_account is the destination account address for acceptor of the payment", "in": "query", "required": false, "type": "string"}, {"name": "card_number", "description": "card_number is the card number of the payer", "in": "query", "required": false, "type": "string"}, {"name": "amount", "description": "amount is the amount to be paid", "in": "query", "required": false, "type": "string"}, {"name": "asset", "description": "asset is the asset type of the amount", "in": "query", "required": false, "type": "string"}, {"name": "description", "description": "description is the description of the payment", "in": "query", "required": false, "type": "string"}, {"name": "reference_id", "description": "reference_id is actually idempotency key for making create draft transaction idempotent", "in": "query", "required": false, "type": "string"}, {"name": "metadata[string]", "description": "metadata is the metadata of the transaction\n\nThis is a request variable of the map type. The query format is \"map_name[key]=value\", e.g. If the map name is Age, the key type is string, and the value type is integer, the query parameter is expressed as Age[\"bob\"]=18", "in": "query", "required": false, "type": "string"}], "tags": ["TransactionService"]}}, "/v1/transaction/user/cards": {"get": {"summary": "FetchUserCards fetch user cards for letting him to chose one for transaction", "operationId": "TransactionService_FetchUserCardsForDraftTransaction", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbFetchUserCardsListForDraftTransactionResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "payer_identifier", "description": "payer_identifier is the mobile / national id / card no of the payer", "in": "query", "required": false, "type": "string"}, {"name": "draft_type", "description": "draft_type is the type of draft transaction", "in": "query", "required": false, "type": "string"}, {"name": "destination_account", "description": "destination_account is the destination account address for acceptor of the payment", "in": "query", "required": false, "type": "string"}, {"name": "card_number", "description": "card_number is the card number of the payer", "in": "query", "required": false, "type": "string"}, {"name": "amount", "description": "amount is the amount to be paid", "in": "query", "required": false, "type": "string"}, {"name": "asset", "description": "asset is the asset type of the amount", "in": "query", "required": false, "type": "string"}, {"name": "description", "description": "description is the description of the payment", "in": "query", "required": false, "type": "string"}, {"name": "reference_id", "description": "reference_id is actually idempotency key for making create draft transaction idempotent", "in": "query", "required": false, "type": "string"}, {"name": "metadata[string][string]", "description": "metadata is the metadata of the transaction\n\nThis is a request variable of the map type. The query format is \"map_name[key]=value\", e.g. If the map name is Age, the key type is string, and the value type is integer, the query parameter is expressed as Age[\"bob\"]=18", "in": "query", "required": false, "type": "string"}], "tags": ["TransactionService"]}}}, "definitions": {"pbCommitDraftTransactionResponse": {"type": "object", "properties": {"error": {"type": "boolean", "description": "whether the request returns error or no."}, "message": {"type": "string", "title": "error message if occurred or success message"}, "transaction": {"$ref": "#/definitions/pbTransaction", "title": "transaction is the transaction object"}}, "title": "CommitDraftTransactionResponse is the response object for commit draft transaction"}, "pbCommitTransactionResponse": {"type": "object", "properties": {"error": {"type": "boolean", "description": "whether the request returns error or no."}, "message": {"type": "string", "title": "error message if occurred or success message"}, "transaction": {"$ref": "#/definitions/pbTransaction", "title": "transaction is the transaction object"}}, "title": "CommitTransactionResponse is the response object for commit transaction"}, "pbDraftTransaction": {"type": "object", "properties": {"identifier": {"type": "string", "title": "unique external identifier for internal-external system identifier isolation"}, "draft_type": {"type": "string", "title": "represent type of draft transaction"}, "totp": {"type": "string", "title": "bcrypted TOTP code for validating transaction during committing it"}, "reference_id": {"type": "string", "title": "idempotency key for making create draft transaction idempotent"}, "metadata": {"type": "object", "additionalProperties": {"type": "string"}, "title": "transaction metadatas"}, "expires_at": {"type": "string", "title": "when the draft will expires"}, "updated_at": {"type": "string", "title": "when draft was updated"}, "created_at": {"type": "string", "title": "when draft was created"}}, "title": "DraftTransaction is the draft transaction object"}, "pbDraftTransactionResponse": {"type": "object", "properties": {"error": {"type": "boolean", "description": "whether the request returns error or no."}, "message": {"type": "string", "title": "error message if occurred or success message"}, "draft_transaction": {"$ref": "#/definitions/pbDraftTransaction", "title": "payment draft id"}}, "title": "DraftTransactionResponse is the response object for draft transaction"}, "pbFetchUserCardsListForDraftTransactionResponse": {"type": "object", "properties": {"error": {"type": "boolean", "description": "whether the request returns error or no."}, "message": {"type": "string", "title": "error message if occurred or success message"}, "card_numbers_list": {"type": "array", "items": {"type": "string"}, "title": "transaction is the transaction object"}, "count": {"type": "integer", "format": "int32", "title": "count of cards"}}, "title": "FetchUserCardsListForDraftTransactionResponse is the response object for fetch user cards for draft transaction"}, "pbTransaction": {"type": "object", "properties": {"source_account": {"type": "string", "title": "source_account is the source account address for payer of the payment"}, "destination_account": {"type": "string", "title": "destination_account is the destination account address for acceptor of the payment"}, "reference_id": {"type": "string", "title": "reference_id is actually idempotency key for making create draft transaction idempotent"}, "amount": {"type": "number", "format": "double", "title": "transaction amount"}, "asset": {"type": "string", "title": "transaction asset type"}, "description": {"type": "string", "title": "transaction description"}, "tracing_id": {"type": "string", "title": "tracing_id is the tracing id for transaction"}, "metadata": {"type": "object", "additionalProperties": {"type": "string"}, "title": "transaction metadata"}}, "title": "Transaction is the transaction object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Authentication token, prefixed by Bear<PERSON>: Bearer <token>", "name": "Authorization", "in": "header"}}, "security": [{"Bearer": []}]}