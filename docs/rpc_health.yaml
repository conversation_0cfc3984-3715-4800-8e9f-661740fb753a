{"swagger": "2.0", "info": {"title": "rpc_health.proto", "version": "version not set"}, "tags": [{"name": "Health"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {}, "definitions": {"pbDetails": {"type": "object", "properties": {"database": {"type": "string", "title": "health status of the database"}, "redis": {"type": "string", "title": "health status of the redis"}, "mongodb": {"type": "string", "title": "health status of the mongodb"}, "message_bus": {"type": "string", "title": "health status of the message bus (NATS)"}, "service_mesh": {"type": "string", "title": "health status of the service mesh (Dapr) or (Builtin NATS based service mesh client)"}}, "title": "Details of the health status of the service and required services"}, "pbHealthCheckResponse": {"type": "object", "properties": {"status": {"type": "string", "title": "health status of the service and required services"}, "timestamp": {"type": "string", "format": "int64", "title": "timestamp of the health check"}, "details": {"$ref": "#/definitions/pbDetails", "title": "details of the health status of the service and required services"}}, "title": "Response for checking health status of the service and required services"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}