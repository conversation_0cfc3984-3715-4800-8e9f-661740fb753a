# Transaction Service API Documentation

This document describes the OpenAPI 3.0 implementation for the Transaction Service, which provides both gRPC and REST API interfaces.

## Overview

The Transaction Service now supports both gRPC and HTTP REST APIs:
- **gRPC Server**: Runs on port 8080 (configurable via `GRPC_LISTENER_HOST`)
- **HTTP Gateway**: Runs on port 8081 (configurable via `HTTP_LISTENER_HOST`)

## API Documentation

### OpenAPI Specifications

- **OpenAPI 3.0 Spec**: [`transaction _service_openapi.yaml`](./transaction _service_openapi.yaml)
- **Swagger 2.0 Spec**: [`transaction _service.swagger.json`](./transaction _service.swagger.json) (auto-generated)

### Available Endpoints

#### Authentication Endpoints

1. **POST /v1/auth/signin**
   - Sign in user with mobile, email, or role
   - Returns authorization response with OTP expiration

2. **POST /v1/auth/signup**
   - Register new user with profile information
   - Returns authorization response with OTP expiration

3. **POST /v1/auth/verify-otp**
   - Verify OTP code and get access/refresh tokens
   - Returns authentication tokens

4. **POST /v1/auth/refresh-token**
   - Refresh existing authentication tokens
   - Returns new authentication tokens

#### User Endpoints

1. **GET /v1/user/context**
   - Get authenticated user's context data
   - Requires Bearer token authentication

## Authentication

The API uses Bearer token authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-token>
```

## Running the Service

### Prerequisites

1. Install protoc plugins:
   ```bash
   make install-proto-deps
   ```

2. Generate proto files:
   ```bash
   make proto
   ```

3. Start dependencies (PostgreSQL, Redis, NATS, MongoDB):
   ```bash
   make dev-up
   ```

### Starting the Service

```bash
make run
```

This will start both:
- gRPC server on `localhost:8080`
- HTTP gateway on `localhost:8081`

## Testing

### Running Tests

```bash
# Run all tests
make test

# Test specific endpoints
go test -v ./test/api_test.go
```

### Manual Testing

#### Using curl for HTTP endpoints:

```bash
# Sign In
curl -X POST http://localhost:8081/v1/auth/signin \
  -H "Content-Type: application/json" \
  -d '{
    "mobile": "+1234567890",
    "email": "<EMAIL>",
    "role": "user"
  }'

# Sign Up
curl -X POST http://localhost:8081/v1/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "mobile": "+1234567890",
    "email": "<EMAIL>",
    "profileType": "individual",
    "firstName": "John",
    "lastName": "Doe",
    "nationalId": "1234567890"
  }'

# Get User Context (requires authentication)
curl -X GET http://localhost:8081/v1/user/context \
  -H "Authorization: Bearer <your-token>"
```

#### Using grpcurl for gRPC endpoints:

```bash
# Sign In
grpcurl -plaintext -d '{
  "mobile": "+1234567890",
  "email": "<EMAIL>",
  "role": "user"
}' localhost:8080 transaction _service.v1.UserService/SignIn
```

## Configuration

Add the following to your environment configuration:

```env
# HTTP Gateway Configuration
HTTP_LISTENER_HOST=0.0.0.0:8081
```

## Development

### Regenerating API Documentation

When you modify the proto files:

1. Update proto definitions with HTTP annotations
2. Regenerate code and documentation:
   ```bash
   make proto
   ```
3. Update the OpenAPI YAML file if needed

### Adding New Endpoints

1. Add the RPC method to `proto/transaction _service.proto`
2. Add HTTP annotations using `google.api.http`
3. Add OpenAPI annotations using `grpc.gateway.protoc_gen_openapiv2.options`
4. Regenerate proto files: `make proto`
5. Update the OpenAPI YAML file
6. Add tests for the new endpoint

## Files Structure

```
docs/
├── README.md                    # This file
├── transaction _service_openapi.yaml    # OpenAPI 3.0 specification
└── transaction _service.swagger.json    # Auto-generated Swagger 2.0 spec

proto/
├── transaction _service.proto           # Proto definitions with HTTP annotations
└── google/api/                  # Google API proto files

pb/
├── transaction _service.pb.go           # Generated Go structs
├── transaction _service_grpc.pb.go      # Generated gRPC server/client
└── transaction _service.pb.gw.go        # Generated HTTP gateway

test/
└── api_test.go                  # API tests for both gRPC and HTTP
```

## Troubleshooting

### Common Issues

1. **Import errors when generating proto files**
   - Ensure all required proto files are in the `proto/` directory
   - Check that protoc plugins are installed: `make install-proto-deps`

2. **HTTP gateway not starting**
   - Check that `HTTP_LISTENER_HOST` is set in your environment
   - Ensure the gRPC server is running before the HTTP gateway

3. **Authentication errors**
   - Verify that the Bearer token is correctly formatted
   - Check that the token is not expired

### Logs

Check the service logs for detailed error information. The service logs both gRPC and HTTP requests.
