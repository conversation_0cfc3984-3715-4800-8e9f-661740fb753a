package health

import (
	"context"
	"errors"
	"time"

	"github.com/dapr/go-sdk/client"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/liveutil/transaction_service/internal/domain"
	"github.com/nats-io/nats.go"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/readpref"
)

type repository struct {
	db         *pgxpool.Pool
	redis      *redis.Client
	mongo      *mongo.Client
	nats       *nats.Conn
	daprClient client.Client
}

// NewRepository creates a new health repository instance
func NewRepository(
	db *pgxpool.Pool,
	redis *redis.Client,
	mongo *mongo.Client,
	nats *nats.Conn,
	daprClient client.Client,
) domain.HealthRepository {
	return &repository{
		db:         db,
		redis:      redis,
		mongo:      mongo,
		nats:       nats,
		daprClient: daprClient,
	}
}

// CheckDatabase checks the PostgreSQL database connection
func (r *repository) CheckDatabase(ctx context.Context) error {
	if r.db == nil {
		return errors.New("database connection is not initialized")
	}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return r.db.Ping(ctx)
}

// CheckRedis checks the Redis connection
func (r *repository) CheckRedis(ctx context.Context) error {
	if r.redis == nil {
		return errors.New("redis connection is not initialized")
	}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return r.redis.Ping(ctx).Err()
}

// CheckMongoDB checks the MongoDB connection
func (r *repository) CheckMongoDB(ctx context.Context) error {
	if r.mongo == nil {
		return errors.New("mongodb connection is not initialized")
	}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return r.mongo.Ping(ctx, readpref.Primary())
}

// CheckMessageBus checks the NATS connection
func (r *repository) CheckMessageBus(ctx context.Context) error {
	if r.nats == nil {
		return errors.New("nats connection is not initialized")
	}

	if !r.nats.IsConnected() {
		return r.nats.LastError()
	}
	return nil
}

// CheckServiceMesh checks the Dapr service mesh connection
func (r *repository) CheckServiceMesh(ctx context.Context) error {
	if r.daprClient == nil {
		return errors.New("dapr client is not initialized")
	}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Try to get the Dapr version as a health check
	_, err := r.daprClient.GetMetadata(ctx)
	return err
}
