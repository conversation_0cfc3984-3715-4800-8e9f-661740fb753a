// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: session.sql

package postgres

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const createUserSession = `-- name: CreateUserSession :one
INSERT INTO sessions (
        identifier,
        user_id,
        expires_in,
        notification,
        meta_data,
        host,
        created_at,
        updated_at
    )
VALUES (
        $1,
        $2,
        $3,
        $4,
        $5,
        $6,
        (CURRENT_TIMESTAMP),
        (CURRENT_TIMESTAMP)
    )
RETURNING id, host, identifier, user_id, expires_in, notification, meta_data, created_at, deleted_at, updated_at
`

type CreateUserSessionParams struct {
	Identifier   string      `json:"identifier"`
	UserID       int64       `json:"user_id"`
	ExpiresIn    time.Time   `json:"expires_in"`
	Notification pgtype.Text `json:"notification"`
	MetaData     []byte      `json:"meta_data"`
	Host         string      `json:"host"`
}

func (q *Queries) CreateUserSession(ctx context.Context, arg CreateUserSessionParams) (Session, error) {
	row := q.db.QueryRow(ctx, createUserSession,
		arg.Identifier,
		arg.UserID,
		arg.ExpiresIn,
		arg.Notification,
		arg.MetaData,
		arg.Host,
	)
	var i Session
	err := row.Scan(
		&i.ID,
		&i.Host,
		&i.Identifier,
		&i.UserID,
		&i.ExpiresIn,
		&i.Notification,
		&i.MetaData,
		&i.CreatedAt,
		&i.DeletedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getSessionById = `-- name: GetSessionById :one
SELECT id, host, identifier, user_id, expires_in, notification, meta_data, created_at, deleted_at, updated_at
FROM sessions
WHERE id = $1
    AND user_id = $2
    AND deleted_at IS NULL
LIMIT 1
`

type GetSessionByIdParams struct {
	ID     int64 `json:"id"`
	UserID int64 `json:"user_id"`
}

func (q *Queries) GetSessionById(ctx context.Context, arg GetSessionByIdParams) (Session, error) {
	row := q.db.QueryRow(ctx, getSessionById, arg.ID, arg.UserID)
	var i Session
	err := row.Scan(
		&i.ID,
		&i.Host,
		&i.Identifier,
		&i.UserID,
		&i.ExpiresIn,
		&i.Notification,
		&i.MetaData,
		&i.CreatedAt,
		&i.DeletedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const renewUserSession = `-- name: RenewUserSession :one
UPDATE sessions
SET
    (meta_data, updated_at, expires_in) = ($3, (CURRENT_TIMESTAMP), $4)
WHERE
    sessions.user_id = $1
    AND sessions.id = $2
    AND sessions.deleted_at IS NULL
RETURNING
    id, host, identifier, user_id, expires_in, notification, meta_data, created_at, deleted_at, updated_at
`

type RenewUserSessionParams struct {
	UserID    int64     `json:"user_id"`
	ID        int64     `json:"id"`
	MetaData  []byte    `json:"meta_data"`
	ExpiresIn time.Time `json:"expires_in"`
}

func (q *Queries) RenewUserSession(ctx context.Context, arg RenewUserSessionParams) (Session, error) {
	row := q.db.QueryRow(ctx, renewUserSession,
		arg.UserID,
		arg.ID,
		arg.MetaData,
		arg.ExpiresIn,
	)
	var i Session
	err := row.Scan(
		&i.ID,
		&i.Host,
		&i.Identifier,
		&i.UserID,
		&i.ExpiresIn,
		&i.Notification,
		&i.MetaData,
		&i.CreatedAt,
		&i.DeletedAt,
		&i.UpdatedAt,
	)
	return i, err
}
