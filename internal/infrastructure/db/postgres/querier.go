// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postgres

import (
	"context"
)

type Querier interface {
	CreateUserAndRelations(ctx context.Context, arg CreateUserAndRelationsParams) (CreateUserAndRelationsRow, error)
	FindMatchesWorkflows(ctx context.Context, arg FindMatchesWorkflowsParams) ([]Workflow, error)
	FindRegexMatchesWorkflows(ctx context.Context, arg FindRegexMatchesWorkflowsParams) ([]Workflow, error)
	GetDraftTransactionByIdentifier(ctx context.Context, identifier string) (TransactionDraft, error)
	GetDraftTransactionByIdentifierAndReference(ctx context.Context, arg GetDraftTransactionByIdentifierAndReferenceParams) (TransactionDraft, error)
	SelectAllActiveWorkflows(ctx context.Context) ([]Workflow, error)
	UpsertTransaction(ctx context.Context, arg UpsertTransactionParams) (TransactionDraft, error)
}

var _ Querier = (*Queries)(nil)
