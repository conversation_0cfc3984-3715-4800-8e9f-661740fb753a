// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postgres

import (
	"database/sql/driver"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

type CardStatus string

const (
	CardStatusISSUING         CardStatus = "ISSUING"
	CardStatusISSUED          CardStatus = "ISSUED"
	CardStatusDELIVERED       CardStatus = "DELIVERED"
	CardStatusLEGALLOCKED     CardStatus = "LEGAL_LOCKED"
	CardStatusPINLOCKED       CardStatus = "PIN_LOCKED"
	CardStatusUSERLOCKED      CardStatus = "USER_LOCKED"
	CardStatusEXPIREDLOCKED   CardStatus = "EXPIRED_LOCKED"
	CardStatusACTIVE          CardStatus = "ACTIVE"
	CardStatusSUSPENDED       CardStatus = "SUSPENDED"
	CardStatusLOST            CardStatus = "LOST"
	CardStatusSTOLEN          CardStatus = "STOLEN"
	CardStatusREPLACEMENT     CardStatus = "REPLACEMENT"
	CardStatusREPLACEDLOCKED  CardStatus = "REPLACED_LOCKED"
	CardStatusREPLACEDEXPIRED CardStatus = "REPLACED_EXPIRED"
	CardStatusREPLACEDBLOCKED CardStatus = "REPLACED_BLOCKED"
	CardStatusREPLACEDDELETED CardStatus = "REPLACED_DELETED"
	CardStatusREPLACED        CardStatus = "REPLACED"
	CardStatusEXPIRED         CardStatus = "EXPIRED"
	CardStatusBLOCKED         CardStatus = "BLOCKED"
	CardStatusDELETED         CardStatus = "DELETED"
)

func (e *CardStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = CardStatus(s)
	case string:
		*e = CardStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for CardStatus: %T", src)
	}
	return nil
}

type NullCardStatus struct {
	CardStatus CardStatus `json:"card_status"`
	Valid      bool       `json:"valid"` // Valid is true if CardStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullCardStatus) Scan(value interface{}) error {
	if value == nil {
		ns.CardStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.CardStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullCardStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.CardStatus), nil
}

type ContactType string

const (
	ContactTypeEMAIL  ContactType = "EMAIL"
	ContactTypeMOBILE ContactType = "MOBILE"
)

func (e *ContactType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ContactType(s)
	case string:
		*e = ContactType(s)
	default:
		return fmt.Errorf("unsupported scan type for ContactType: %T", src)
	}
	return nil
}

type NullContactType struct {
	ContactType ContactType `json:"contact_type"`
	Valid       bool        `json:"valid"` // Valid is true if ContactType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullContactType) Scan(value interface{}) error {
	if value == nil {
		ns.ContactType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ContactType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullContactType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ContactType), nil
}

type IinStatus string

const (
	IinStatusPENDING     IinStatus = "PENDING"
	IinStatusREJECTED    IinStatus = "REJECTED"
	IinStatusACTIVE      IinStatus = "ACTIVE"
	IinStatusINACTIVE    IinStatus = "INACTIVE"
	IinStatusDELETED     IinStatus = "DELETED"
	IinStatusBLOCKED     IinStatus = "BLOCKED"
	IinStatusSUSPENDED   IinStatus = "SUSPENDED"
	IinStatusLEGALLOCKED IinStatus = "LEGAL_LOCKED"
)

func (e *IinStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = IinStatus(s)
	case string:
		*e = IinStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for IinStatus: %T", src)
	}
	return nil
}

type NullIinStatus struct {
	IinStatus IinStatus `json:"iin_status"`
	Valid     bool      `json:"valid"` // Valid is true if IinStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullIinStatus) Scan(value interface{}) error {
	if value == nil {
		ns.IinStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.IinStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullIinStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.IinStatus), nil
}

type ProfileStatus string

const (
	ProfileStatusPENDING  ProfileStatus = "PENDING"
	ProfileStatusFILLED   ProfileStatus = "FILLED"
	ProfileStatusREJECTED ProfileStatus = "REJECTED"
	ProfileStatusAPPROVED ProfileStatus = "APPROVED"
	ProfileStatusLOCKED   ProfileStatus = "LOCKED"
)

func (e *ProfileStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ProfileStatus(s)
	case string:
		*e = ProfileStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for ProfileStatus: %T", src)
	}
	return nil
}

type NullProfileStatus struct {
	ProfileStatus ProfileStatus `json:"profile_status"`
	Valid         bool          `json:"valid"` // Valid is true if ProfileStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullProfileStatus) Scan(value interface{}) error {
	if value == nil {
		ns.ProfileStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ProfileStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullProfileStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ProfileStatus), nil
}

type ProfileType string

const (
	ProfileTypeNATURAL ProfileType = "NATURAL"
	ProfileTypeLEGAL   ProfileType = "LEGAL"
)

func (e *ProfileType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ProfileType(s)
	case string:
		*e = ProfileType(s)
	default:
		return fmt.Errorf("unsupported scan type for ProfileType: %T", src)
	}
	return nil
}

type NullProfileType struct {
	ProfileType ProfileType `json:"profile_type"`
	Valid       bool        `json:"valid"` // Valid is true if ProfileType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullProfileType) Scan(value interface{}) error {
	if value == nil {
		ns.ProfileType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ProfileType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullProfileType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ProfileType), nil
}

type TransactionDraftType string

const (
	TransactionDraftTypeMOBILE     TransactionDraftType = "MOBILE"
	TransactionDraftTypeNATIONALID TransactionDraftType = "NATIONAL_ID"
	TransactionDraftTypeCARDNUMBER TransactionDraftType = "CARD_NUMBER"
)

func (e *TransactionDraftType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = TransactionDraftType(s)
	case string:
		*e = TransactionDraftType(s)
	default:
		return fmt.Errorf("unsupported scan type for TransactionDraftType: %T", src)
	}
	return nil
}

type NullTransactionDraftType struct {
	TransactionDraftType TransactionDraftType `json:"transaction_draft_type"`
	Valid                bool                 `json:"valid"` // Valid is true if TransactionDraftType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullTransactionDraftType) Scan(value interface{}) error {
	if value == nil {
		ns.TransactionDraftType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.TransactionDraftType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullTransactionDraftType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.TransactionDraftType), nil
}

type WorkflowType string

const (
	WorkflowTypeNUMSCRIPT    WorkflowType = "NUMSCRIPT"
	WorkflowTypeWORKFLOWYAML WorkflowType = "WORKFLOW_YAML"
)

func (e *WorkflowType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = WorkflowType(s)
	case string:
		*e = WorkflowType(s)
	default:
		return fmt.Errorf("unsupported scan type for WorkflowType: %T", src)
	}
	return nil
}

type NullWorkflowType struct {
	WorkflowType WorkflowType `json:"workflow_type"`
	Valid        bool         `json:"valid"` // Valid is true if WorkflowType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullWorkflowType) Scan(value interface{}) error {
	if value == nil {
		ns.WorkflowType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.WorkflowType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullWorkflowType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.WorkflowType), nil
}

type Card struct {
	// card internal system unique id
	ID int64 `json:"id"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// owner of card external identifier
	UserIdentifier string `json:"user_identifier"`
	// card status determinable value
	Status CardStatus `json:"status"`
	// card issuer identifier id
	Iin int64 `json:"iin"`
	// card type id
	CardType int64 `json:"card_type"`
	// is this card default card for user or not
	IsUserDefaultCard bool `json:"is_user_default_card"`
	// card issuer identifier code number
	IinCode string `json:"iin_code"`
	// card / service or issuer defined card type code
	CardTypeCode string `json:"card_type_code"`
	// card owner account number
	AccountNumber string `json:"account_number"`
	// Luhn Checksum digit for given (iin + account_number) string
	LuhnDigit string `json:"luhn_digit"`
	// CVV1 for magnetic stripe (service code 101)
	Cvv1 string `json:"cvv1"`
	// iCVV for chip cards (service code 201)
	Icvv string `json:"icvv"`
	// CVV2 for card-not-present transactions (service code 999)
	Cvv2 string `json:"cvv2"`
	// track 1 data for magnetic stripe cards
	Track1 string `json:"track_1"`
	// track 2 data for magnetic stripe cards
	Track2 string `json:"track_2"`
	// application protocol data units for chip cards
	ApplicationProtocolDataUnits []string `json:"application_protocol_data_units"`
	// EMV profile in XML format
	EmvXml string `json:"emv_xml"`
	// bcrypted random unique 4 or 6 digit pin code number that used for signing transactions
	PinCode string `json:"pin_code"`
	// complete card number string using [iin(4) + account_number(11) + luhn_checksum(1) = 16 character
	PrimaryAccountNumber string `json:"primary_account_number"`
	// issued card valid before this year
	ExpireYear string `json:"expire_year"`
	// issued card valid before this year / month
	ExpireMonth string `json:"expire_month"`
	// card metadatas
	MetaData []byte `json:"meta_data"`
	// when the card will expires
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	// when card was created
	CreatedAt time.Time `json:"created_at"`
	// when card was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when card was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type CardType struct {
	// card type internal system unique id
	ID int64 `json:"id"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// card type code
	Code string `json:"code"`
	// card type name
	Name string `json:"name"`
	// card type description
	Description pgtype.Text `json:"description"`
	// card type metadatas
	MetaData []byte `json:"meta_data"`
	// is this card type default type for creating signed up users default card or not
	IsDefault bool `json:"is_default"`
	// card issuer identifier id
	Iin int64 `json:"iin"`
	// when card type was created
	CreatedAt time.Time `json:"created_at"`
	// when card type was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when card type was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type Contact struct {
	// contact unique id
	ID int64 `json:"id"`
	// unique external identifier for inter system internal-external identifier separation
	Identifier  string      `json:"identifier"`
	ContactType ContactType `json:"contact_type"`
	// related user id to determining session owner account
	UserID int64 `json:"user_id"`
	// contact primary mobile phone number for authorization use
	Mobile string `json:"mobile"`
	// holds TOTP bcrypted pass code
	MobileTotp pgtype.Text `json:"mobile_totp"`
	// sets to true if user verified his mobile by first time otp verification
	IsMobileVerified bool `json:"is_mobile_verified"`
	// holds by mobile OTP verification code expire time
	MobileTotpExpiresAt pgtype.Timestamptz `json:"mobile_totp_expires_at"`
	// contact primary e-mail address
	Email string `json:"email"`
	// holds TOTP bcrypted pass code
	EmailTotp pgtype.Text `json:"email_totp"`
	// sets to true if user verified his email by first time otp verification
	IsEmailVerified bool `json:"is_email_verified"`
	// holds by e-mail OTP verification code expire time
	EmailTotpExpiresAt pgtype.Timestamptz `json:"email_totp_expires_at"`
	// contact metadatas
	MetaData []byte `json:"meta_data"`
	// when contact was created
	CreatedAt time.Time `json:"created_at"`
	// when contact was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when contact was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type IssuerIdentifier struct {
	// iin registry internal system unique id
	ID int64 `json:"id"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// card issuer identifier number
	Iin string `json:"iin"`
	// iin registry status determinable value
	Status IinStatus `json:"status"`
	// issuer owner user external identifier
	IssuerUserIdentifier string `json:"issuer_user_identifier"`
	// issuer name
	IssuerName string `json:"issuer_name"`
	// issuer logo url
	IssuerLogo pgtype.Text `json:"issuer_logo"`
	// issuer website url
	IssuerUrl pgtype.Text `json:"issuer_url"`
	// 3DES key for encrypting card data
	DesKey []byte `json:"des_key"`
	// is this iin default issuer for creating signed up users default card or not
	IsDefault bool `json:"is_default"`
	// iin registry metadatas
	MetaData []byte `json:"meta_data"`
	// when the iin registry will expires
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	// when iin registry was created
	CreatedAt time.Time `json:"created_at"`
	// when iin registry was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when iin registry was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type Profile struct {
	// profile unique id
	ID int64 `json:"id"`
	// unique external identifier for inter system internal-external identifier separation
	Identifier string `json:"identifier"`
	// related user id to determining session owner account
	UserID int64 `json:"user_id"`
	// legal or natural person type deffination
	ProfileType ProfileType `json:"profile_type"`
	// user first name
	FirstName string `json:"first_name"`
	// user last name
	LastName string `json:"last_name"`
	// user unique personal national id-code
	NationalID string `json:"national_id"`
	// profile control status
	Status ProfileStatus `json:"status"`
	// profile metadatas
	MetaData []byte `json:"meta_data"`
	// when profile was created
	CreatedAt time.Time `json:"created_at"`
	// when profile was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when profile was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type Session struct {
	// session unique id
	ID int64 `json:"id"`
	// session creation request host name (for SSO use)
	Host string `json:"host"`
	// unique external identifier for inter system internal-external identifier separation
	Identifier string `json:"identifier"`
	// related user id to determining session owner account
	UserID int64 `json:"user_id"`
	// when session expires (only refresh token would updates this field)
	ExpiresIn time.Time `json:"expires_in"`
	// notification provider token for sending notifications by device
	Notification pgtype.Text `json:"notification"`
	// meta data of session like IP, UserAgent etc...
	MetaData []byte `json:"meta_data"`
	// when session created
	CreatedAt time.Time `json:"created_at"`
	// when session deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
	// when session updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
}

type TransactionDraft struct {
	// draft internal system unique id
	ID int64 `json:"id"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// represent type of draft transaction
	DraftType TransactionDraftType `json:"draft_type"`
	// bcrypted TOTP code for validating transaction during committing it
	Totp string `json:"totp"`
	// idempotency key for making create draft transaction idempotent
	ReferenceID string `json:"reference_id"`
	// transaction metadatas
	MetaData []byte `json:"meta_data"`
	// card number for card number draft type
	CardNumber string `json:"card_number"`
	// source account address for payer of the payment
	SourceAccount string `json:"source_account"`
	// destination account address for acceptor of the payment
	DestinationAccount string `json:"destination_account"`
	// asset for transaction flow to match with
	Asset string `json:"asset"`
	// amount for transaction flow to match with
	Amount float64 `json:"amount"`
	// when the draft will expires
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	// when draft was updated
	UpdatedAt time.Time `json:"updated_at"`
	// when draft was created
	CreatedAt time.Time `json:"created_at"`
}

type User struct {
	// user unique id
	ID int64 `json:"id"`
	// unique external identifier for inter system internal-external identifier separation
	Identifier string `json:"identifier"`
	// is user approved or no
	Approved bool `json:"approved"`
	// is user banned or no
	Banned bool `json:"banned"`
	// user metadatas
	MetaData []byte `json:"meta_data"`
	// user assigned roles for permission controls
	Roles []string `json:"roles"`
	// expire time of user, if not sets then user valid for unlimited time
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	// when user was created
	CreatedAt time.Time `json:"created_at"`
	// when user was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when user was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type Workflow struct {
	// transaction flow internal system unique id
	ID int64 `json:"id"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// unique slug for transaction flow
	Slug string `json:"slug"`
	// description for transaction flow
	Description string `json:"description"`
	// is transaction flow active
	IsActive bool `json:"is_active"`
	// allow or disallow transaction route
	Allowed bool `json:"allowed"`
	// formance workflow id for transaction flow
	WorkflowID string `json:"workflow_id"`
	// type of workflow
	WorkflowType WorkflowType `json:"workflow_type"`
	// formance ledger id for transaction flow to separate transaction flows between different ledgers
	SourceLedger string `json:"source_ledger"`
	// source account address pattern that contains "Domain Segments" for payer to match with
	SourceAddressPattern string `json:"source_address_pattern"`
	// destination account address pattern that contains "Domain Segments" for acceptor of the payment to match with
	DestinationAddressPattern string `json:"destination_address_pattern"`
	// priority override for transaction flow
	PriorityOverride int32 `json:"priority_override"`
	// asset for transaction flow to match with
	Asset string `json:"asset"`
	// maximum amount for transaction flow to execute
	UpAmountThreshold float64 `json:"up_amount_threshold"`
	// minimum amount for transaction flow to execute
	DownAmountThreshold float64 `json:"down_amount_threshold"`
	// transaction flow metadatas
	MetaData []byte `json:"meta_data"`
	// workflow binary data for transaction flow execution, it can contains Numscript script or Formance Orchestration workflow yaml file content
	WorkflowData []byte `json:"workflow_data"`
	// when transaction flow was created
	CreatedAt time.Time `json:"created_at"`
	// when transaction flow was updated
	UpdatedAt time.Time `json:"updated_at"`
	// when transaction flow was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}
