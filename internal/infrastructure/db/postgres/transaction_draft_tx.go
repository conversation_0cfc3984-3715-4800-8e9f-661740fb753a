package postgres

import (
	"context"
)

// UpsertTransactionDraftTxParams is the params for UpsertTransactionDraftTx.
type UpsertTransactionDraftTxParams struct {
	UpsertTransactionParams UpsertTransactionParams
	AfterUpsert             func(transaction TransactionDraft) error
}

// UpsertTransactionDraftTxResult is the result for UpsertTransactionDraftTx.
type UpsertTransactionDraftTxResult struct {
	TransactionDraft TransactionDraft `json:"transaction_draft"`
}

// UpsertTransactionDraftTx creates a new transaction draft and then calls the AfterCreate function.
func (store *SQLStore) UpsertTransactionDraftTx(ctx context.Context, arg UpsertTransactionDraftTxParams) (UpsertTransactionDraftTxResult, error) {
	var result UpsertTransactionDraftTxResult

	err := store.execTx(ctx, func(q *Queries) error {
		var err error

		result.TransactionDraft, err = q.UpsertTransaction(ctx, arg.UpsertTransactionParams)
		if err != nil {
			return err
		}

		return arg.AfterUpsert(result.TransactionDraft)
	})

	return result, err
}
