// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: domain.sql

package postgres

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

const createUserAndRelations = `-- name: CreateUserAndRelations :one
WITH new_user AS (
    INSERT INTO users (
            identifier,
            expires_at,
            created_at,
            updated_at
            
        )
    VALUES (
            $1,
            $2,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        )
    RETURNING id, identifier, approved, banned, meta_data, roles, expires_at, created_at, updated_at, deleted_at
),
new_contact AS (
    INSERT INTO contacts (
            identifier,
            contact_type,
            user_id,
            mobile,
            mobile_totp,
            mobile_totp_expires_at,
            email,
            email_totp,
            email_totp_expires_at,
            created_at,
            updated_at
        )
    VALUES (
            $14,
            $3,
            (
                SELECT id
                FROM new_user
            ),
            $4,
            $5,
            $6,
            $7,
            $8,
            $9,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        )
    RETURNING id, identifier, contact_type, user_id, mobile, mobile_totp, is_mobile_verified, mobile_totp_expires_at, email, email_totp, is_email_verified, email_totp_expires_at, meta_data, created_at, updated_at, deleted_at
),
new_profile AS (
    INSERT INTO profiles (
            identifier,
            user_id,
            profile_type,
            first_name,
            last_name,
            national_id,
            created_at,
            updated_at
        )
    VALUES (
            $15,
            (
                SELECT id
                FROM new_user
            ),
            $10,
            $11,
            $12,
            $13,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        )
    RETURNING id, identifier, user_id, profile_type, first_name, last_name, national_id, status, meta_data, created_at, updated_at, deleted_at
)
SELECT u.id as user_id,
    u.identifier as user_identifier,
    u.approved as user_approved,
    u.banned as user_banned,
    u.meta_data as user_meta_data,
    u.roles as user_roles,
    u.created_at as user_created_at,
    c.id as contact_id,
    c.identifier as contact_identifier,
    c.mobile as contact_mobile,
    c.email as contact_email,
    c.meta_data as contact_meta_data,
    c.created_at as contact_created_at,
    p.id as profile_id,
    p.identifier as profile_identifier,
    p.profile_type as profile_type,
    p.first_name as profile_first_name,
    p.last_name as profile_last_name,
    p.national_id as profile_national_id,
    p.status as profile_status,
    p.meta_data as profile_meta_data,
    p.created_at as profile_created_at
FROM new_user u
    JOIN new_contact c ON c.user_id = u.id
    JOIN new_profile p ON p.user_id = u.id
`

type CreateUserAndRelationsParams struct {
	Identifier          string             `json:"identifier"`
	ExpiresAt           pgtype.Timestamptz `json:"expires_at"`
	ContactType         ContactType        `json:"contact_type"`
	Mobile              string             `json:"mobile"`
	MobileTotp          pgtype.Text        `json:"mobile_totp"`
	MobileTotpExpiresAt pgtype.Timestamptz `json:"mobile_totp_expires_at"`
	Email               string             `json:"email"`
	EmailTotp           pgtype.Text        `json:"email_totp"`
	EmailTotpExpiresAt  pgtype.Timestamptz `json:"email_totp_expires_at"`
	ProfileType         ProfileType        `json:"profile_type"`
	FirstName           string             `json:"first_name"`
	LastName            string             `json:"last_name"`
	NationalID          string             `json:"national_id"`
	ContactIdentifier   string             `json:"contact_identifier"`
	ProfileIdentifier   string             `json:"profile_identifier"`
}

type CreateUserAndRelationsRow struct {
	UserID            int64         `json:"user_id"`
	UserIdentifier    string        `json:"user_identifier"`
	UserApproved      bool          `json:"user_approved"`
	UserBanned        bool          `json:"user_banned"`
	UserMetaData      []byte        `json:"user_meta_data"`
	UserRoles         []string      `json:"user_roles"`
	UserCreatedAt     time.Time     `json:"user_created_at"`
	ContactID         int64         `json:"contact_id"`
	ContactIdentifier string        `json:"contact_identifier"`
	ContactMobile     string        `json:"contact_mobile"`
	ContactEmail      string        `json:"contact_email"`
	ContactMetaData   []byte        `json:"contact_meta_data"`
	ContactCreatedAt  time.Time     `json:"contact_created_at"`
	ProfileID         int64         `json:"profile_id"`
	ProfileIdentifier string        `json:"profile_identifier"`
	ProfileType       ProfileType   `json:"profile_type"`
	ProfileFirstName  string        `json:"profile_first_name"`
	ProfileLastName   string        `json:"profile_last_name"`
	ProfileNationalID string        `json:"profile_national_id"`
	ProfileStatus     ProfileStatus `json:"profile_status"`
	ProfileMetaData   []byte        `json:"profile_meta_data"`
	ProfileCreatedAt  time.Time     `json:"profile_created_at"`
}

func (q *Queries) CreateUserAndRelations(ctx context.Context, arg CreateUserAndRelationsParams) (CreateUserAndRelationsRow, error) {
	row := q.db.QueryRow(ctx, createUserAndRelations,
		arg.Identifier,
		arg.ExpiresAt,
		arg.ContactType,
		arg.Mobile,
		arg.MobileTotp,
		arg.MobileTotpExpiresAt,
		arg.Email,
		arg.EmailTotp,
		arg.EmailTotpExpiresAt,
		arg.ProfileType,
		arg.FirstName,
		arg.LastName,
		arg.NationalID,
		arg.ContactIdentifier,
		arg.ProfileIdentifier,
	)
	var i CreateUserAndRelationsRow
	err := row.Scan(
		&i.UserID,
		&i.UserIdentifier,
		&i.UserApproved,
		&i.UserBanned,
		&i.UserMetaData,
		&i.UserRoles,
		&i.UserCreatedAt,
		&i.ContactID,
		&i.ContactIdentifier,
		&i.ContactMobile,
		&i.ContactEmail,
		&i.ContactMetaData,
		&i.ContactCreatedAt,
		&i.ProfileID,
		&i.ProfileIdentifier,
		&i.ProfileType,
		&i.ProfileFirstName,
		&i.ProfileLastName,
		&i.ProfileNationalID,
		&i.ProfileStatus,
		&i.ProfileMetaData,
		&i.ProfileCreatedAt,
	)
	return i, err
}
