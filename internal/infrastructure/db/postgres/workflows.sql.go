// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: workflows.sql

package postgres

import (
	"context"
)

const findMatchesWorkflows = `-- name: FindMatchesWorkflows :many
SELECT id, identifier, slug, description, is_active, allowed, workflow_id, workflow_type, source_ledger, source_address_pattern, destination_address_pattern, priority_override, asset, up_amount_threshold, down_amount_threshold, meta_data, workflow_data, created_at, updated_at, deleted_at
FROM workflows
WHERE $1 LIKE REPLACE(source_address_pattern, '*', '%')
    AND $2 LIKE REPLACE(destination_address_pattern, '*', '%')
    AND down_amount_threshold <= $3
    AND up_amount_threshold >= $4
    AND is_active = true
    AND deleted_at IS NULL
`

type FindMatchesWorkflowsParams struct {
	SourceAddressPattern      string  `json:"source_address_pattern"`
	DestinationAddressPattern string  `json:"destination_address_pattern"`
	DownAmountThreshold       float64 `json:"down_amount_threshold"`
	UpAmountThreshold         float64 `json:"up_amount_threshold"`
}

func (q *Queries) FindMatchesWorkflows(ctx context.Context, arg FindMatchesWorkflowsParams) ([]Workflow, error) {
	rows, err := q.db.Query(ctx, findMatchesWorkflows,
		arg.SourceAddressPattern,
		arg.DestinationAddressPattern,
		arg.DownAmountThreshold,
		arg.UpAmountThreshold,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Workflow{}
	for rows.Next() {
		var i Workflow
		if err := rows.Scan(
			&i.ID,
			&i.Identifier,
			&i.Slug,
			&i.Description,
			&i.IsActive,
			&i.Allowed,
			&i.WorkflowID,
			&i.WorkflowType,
			&i.SourceLedger,
			&i.SourceAddressPattern,
			&i.DestinationAddressPattern,
			&i.PriorityOverride,
			&i.Asset,
			&i.UpAmountThreshold,
			&i.DownAmountThreshold,
			&i.MetaData,
			&i.WorkflowData,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const findRegexMatchesWorkflows = `-- name: FindRegexMatchesWorkflows :many
SELECT id, identifier, slug, description, is_active, allowed, workflow_id, workflow_type, source_ledger, source_address_pattern, destination_address_pattern, priority_override, asset, up_amount_threshold, down_amount_threshold, meta_data, workflow_data, created_at, updated_at, deleted_at
FROM workflows
WHERE REGEX_MATCHES(
        $1,
        '^' || REPLACE(source_domain_pattern, '*', '[^:]*') || '$'
    ) IS NOT NULL
    AND REGEX_MATCHES(
        $2,
        '^' || REPLACE(destination_domain_pattern, '*', '[^:]*') || '$'
    ) IS NOT NULL
    AND down_amount_threshold <= $3
    AND up_amount_threshold >= $4
    AND is_active = true
    AND deleted_at IS NULL
`

type FindRegexMatchesWorkflowsParams struct {
	RegexMatches        interface{} `json:"regex_matches"`
	RegexMatches_2      interface{} `json:"regex_matches_2"`
	DownAmountThreshold float64     `json:"down_amount_threshold"`
	UpAmountThreshold   float64     `json:"up_amount_threshold"`
}

func (q *Queries) FindRegexMatchesWorkflows(ctx context.Context, arg FindRegexMatchesWorkflowsParams) ([]Workflow, error) {
	rows, err := q.db.Query(ctx, findRegexMatchesWorkflows,
		arg.RegexMatches,
		arg.RegexMatches_2,
		arg.DownAmountThreshold,
		arg.UpAmountThreshold,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Workflow{}
	for rows.Next() {
		var i Workflow
		if err := rows.Scan(
			&i.ID,
			&i.Identifier,
			&i.Slug,
			&i.Description,
			&i.IsActive,
			&i.Allowed,
			&i.WorkflowID,
			&i.WorkflowType,
			&i.SourceLedger,
			&i.SourceAddressPattern,
			&i.DestinationAddressPattern,
			&i.PriorityOverride,
			&i.Asset,
			&i.UpAmountThreshold,
			&i.DownAmountThreshold,
			&i.MetaData,
			&i.WorkflowData,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const selectAllActiveWorkflows = `-- name: SelectAllActiveWorkflows :many
SELECT id, identifier, slug, description, is_active, allowed, workflow_id, workflow_type, source_ledger, source_address_pattern, destination_address_pattern, priority_override, asset, up_amount_threshold, down_amount_threshold, meta_data, workflow_data, created_at, updated_at, deleted_at
FROM workflows
WHERE is_active IS TRUE
    AND deleted_at IS NOT NULL
ORDER BY priority_override ASC
`

func (q *Queries) SelectAllActiveWorkflows(ctx context.Context) ([]Workflow, error) {
	rows, err := q.db.Query(ctx, selectAllActiveWorkflows)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Workflow{}
	for rows.Next() {
		var i Workflow
		if err := rows.Scan(
			&i.ID,
			&i.Identifier,
			&i.Slug,
			&i.Description,
			&i.IsActive,
			&i.Allowed,
			&i.WorkflowID,
			&i.WorkflowType,
			&i.SourceLedger,
			&i.SourceAddressPattern,
			&i.DestinationAddressPattern,
			&i.PriorityOverride,
			&i.Asset,
			&i.UpAmountThreshold,
			&i.DownAmountThreshold,
			&i.MetaData,
			&i.WorkflowData,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
