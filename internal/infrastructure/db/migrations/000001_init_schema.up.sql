CREATE TYPE "contact_type" AS ENUM (
  '<PERSON>MA<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>'
);

CREATE TYPE "profile_status" AS ENUM (
  'PENDING',
  'FILLED',
  'REJECTED',
  'APPROVED',
  'LOCKED'
);

CREATE TYPE "profile_type" AS ENUM (
  'NATURAL',
  'LEGAL'
);

CREATE TABLE "users" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "approved" boolean NOT NULL DEFAULT false,
  "banned" boolean NOT NULL DEFAULT false,
  "meta_data" jsonb NOT NULL DEFAULT '{}',
  "roles" text[] NOT NULL DEFAULT '{USER}',
  "expires_at" timestamptz,
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "updated_at" timestamptz,
  "deleted_at" timestamptz
);

CREATE TABLE "contacts" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "contact_type" contact_type NOT NULL DEFAULT 'MOBILE',
  "user_id" bigserial NOT NULL,
  "mobile" varchar(16) UNIQUE NOT NULL,
  "mobile_totp" varchar(256),
  "is_mobile_verified" boolean NOT NULL DEFAULT false,
  "mobile_totp_expires_at" timestamptz,
  "email" varchar(256) UNIQUE NOT NULL,
  "email_totp" varchar(256),
  "is_email_verified" boolean NOT NULL DEFAULT false,
  "email_totp_expires_at" timestamptz,
  "meta_data" jsonb NOT NULL DEFAULT '{}',
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "updated_at" timestamptz,
  "deleted_at" timestamptz
);

CREATE TABLE "profiles" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "user_id" bigserial NOT NULL,
  "profile_type" profile_type NOT NULL,
  "first_name" varchar(128) NOT NULL,
  "last_name" varchar(128) NOT NULL,
  "national_id" varchar(32) UNIQUE NOT NULL,
  "status" profile_status NOT NULL DEFAULT 'PENDING',
  "meta_data" jsonb NOT NULL DEFAULT '{}',
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "updated_at" timestamptz,
  "deleted_at" timestamptz
);

CREATE TABLE "sessions" (
  "id" bigserial PRIMARY KEY NOT NULL,
  "host" varchar(256) NOT NULL,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "user_id" bigserial NOT NULL,
  "expires_in" timestamptz NOT NULL,
  "notification" varchar(256) DEFAULT '',
  "meta_data" jsonb DEFAULT '{}',
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "deleted_at" timestamptz,
  "updated_at" timestamptz
);

CREATE INDEX ON "users" ("id");

CREATE INDEX ON "users" ("identifier");

CREATE INDEX ON "users" ("deleted_at");

CREATE INDEX ON "users" ("id", "identifier", "deleted_at");

CREATE INDEX ON "users" ("id", "identifier", "banned", "approved", "deleted_at");

CREATE INDEX ON "users" ("banned", "approved");

CREATE INDEX ON "contacts" ("id");

CREATE INDEX ON "contacts" ("identifier");

CREATE INDEX ON "contacts" ("deleted_at");

CREATE INDEX ON "contacts" ("id", "identifier", "deleted_at");

CREATE INDEX ON "contacts" ("id", "identifier", "contact_type", "mobile", "email");

CREATE INDEX ON "profiles" ("id");

CREATE INDEX ON "profiles" ("identifier");

CREATE INDEX ON "profiles" ("deleted_at");

CREATE INDEX ON "profiles" ("id", "identifier", "deleted_at");

CREATE INDEX ON "profiles" ("id", "identifier", "profile_type", "status", "national_id");

CREATE INDEX ON "sessions" ("deleted_at");

CREATE INDEX ON "sessions" ("host");

CREATE INDEX ON "sessions" ("id", "deleted_at");

CREATE INDEX ON "sessions" ("user_id", "deleted_at");

COMMENT ON COLUMN "users"."id" IS 'user unique id';

COMMENT ON COLUMN "users"."identifier" IS 'unique external identifier for inter system internal-external identifier separation';

COMMENT ON COLUMN "users"."approved" IS 'is user approved or no';

COMMENT ON COLUMN "users"."banned" IS 'is user banned or no';

COMMENT ON COLUMN "users"."meta_data" IS 'user metadatas';

COMMENT ON COLUMN "users"."roles" IS 'user assigned roles for permission controls';

COMMENT ON COLUMN "users"."expires_at" IS 'expire time of user, if not sets then user valid for unlimited time';

COMMENT ON COLUMN "users"."created_at" IS 'when user was created';

COMMENT ON COLUMN "users"."updated_at" IS 'when user was updated';

COMMENT ON COLUMN "users"."deleted_at" IS 'when user was deleted';

COMMENT ON COLUMN "contacts"."id" IS 'contact unique id';

COMMENT ON COLUMN "contacts"."identifier" IS 'unique external identifier for inter system internal-external identifier separation';

COMMENT ON COLUMN "contacts"."user_id" IS 'related user id to determining session owner account';

COMMENT ON COLUMN "contacts"."mobile" IS 'contact primary mobile phone number for authorization use';

COMMENT ON COLUMN "contacts"."mobile_totp" IS 'holds TOTP bcrypted pass code';

COMMENT ON COLUMN "contacts"."is_mobile_verified" IS 'sets to true if user verified his mobile by first time otp verification';

COMMENT ON COLUMN "contacts"."mobile_totp_expires_at" IS 'holds by mobile OTP verification code expire time';

COMMENT ON COLUMN "contacts"."email" IS 'contact primary e-mail address';

COMMENT ON COLUMN "contacts"."email_totp" IS 'holds TOTP bcrypted pass code';

COMMENT ON COLUMN "contacts"."is_email_verified" IS 'sets to true if user verified his email by first time otp verification';

COMMENT ON COLUMN "contacts"."email_totp_expires_at" IS 'holds by e-mail OTP verification code expire time';

COMMENT ON COLUMN "contacts"."meta_data" IS 'contact metadatas';

COMMENT ON COLUMN "contacts"."created_at" IS 'when contact was created';

COMMENT ON COLUMN "contacts"."updated_at" IS 'when contact was updated';

COMMENT ON COLUMN "contacts"."deleted_at" IS 'when contact was deleted';

COMMENT ON COLUMN "profiles"."id" IS 'profile unique id';

COMMENT ON COLUMN "profiles"."identifier" IS 'unique external identifier for inter system internal-external identifier separation';

COMMENT ON COLUMN "profiles"."user_id" IS 'related user id to determining session owner account';

COMMENT ON COLUMN "profiles"."profile_type" IS 'legal or natural person type deffination';

COMMENT ON COLUMN "profiles"."first_name" IS 'user first name';

COMMENT ON COLUMN "profiles"."last_name" IS 'user last name';

COMMENT ON COLUMN "profiles"."national_id" IS 'user unique personal national id-code';

COMMENT ON COLUMN "profiles"."status" IS 'profile control status';

COMMENT ON COLUMN "profiles"."meta_data" IS 'profile metadatas';

COMMENT ON COLUMN "profiles"."created_at" IS 'when profile was created';

COMMENT ON COLUMN "profiles"."updated_at" IS 'when profile was updated';

COMMENT ON COLUMN "profiles"."deleted_at" IS 'when profile was deleted';

COMMENT ON COLUMN "sessions"."id" IS 'session unique id';

COMMENT ON COLUMN "sessions"."host" IS 'session creation request host name (for SSO use)';

COMMENT ON COLUMN "sessions"."identifier" IS 'unique external identifier for inter system internal-external identifier separation';

COMMENT ON COLUMN "sessions"."user_id" IS 'related user id to determining session owner account';

COMMENT ON COLUMN "sessions"."expires_in" IS 'when session expires (only refresh token would updates this field)';

COMMENT ON COLUMN "sessions"."notification" IS 'notification provider token for sending notifications by device';

COMMENT ON COLUMN "sessions"."meta_data" IS 'meta data of session like IP, UserAgent etc...';

COMMENT ON COLUMN "sessions"."created_at" IS 'when session created';

COMMENT ON COLUMN "sessions"."deleted_at" IS 'when session deleted';

COMMENT ON COLUMN "sessions"."updated_at" IS 'when session updated';

ALTER TABLE "contacts" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id");

ALTER TABLE "profiles" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id");

ALTER TABLE "sessions" ADD FOREIGN KEY ("user_id") REFERENCES "users" ("id");

















-- SQL dump generated using DBML (dbml.dbdiagram.io)
-- Database: PostgreSQL
-- Generated at: 2025-07-15T03:07:24.091Z

CREATE TYPE "card_status" AS ENUM (
  'ISSUING',
  'ISSUED',
  'DELIVERED',
  'LEGAL_LOCKED',
  'PIN_LOCKED',
  'USER_LOCKED',
  'EXPIRED_LOCKED',
  'ACTIVE',
  'SUSPENDED',
  'LOST',
  'STOLEN',
  'REPLACEMENT',
  'REPLACED_LOCKED',
  'REPLACED_EXPIRED',
  'REPLACED_BLOCKED',
  'REPLACED_DELETED',
  'REPLACED',
  'EXPIRED',
  'BLOCKED',
  'DELETED'
);

CREATE TYPE "iin_status" AS ENUM (
  'PENDING',
  'REJECTED',
  'ACTIVE',
  'INACTIVE',
  'DELETED',
  'BLOCKED',
  'SUSPENDED',
  'LEGAL_LOCKED'
);

CREATE TABLE "cards" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "user_identifier" varchar(64) UNIQUE NOT NULL,
  "status" card_status NOT NULL DEFAULT 'ISSUING',
  "iin" bigint NOT NULL,
  "card_type" bigint NOT NULL,
  "is_user_default_card" bool NOT NULL DEFAULT false,
  "iin_code" varchar(4) NOT NULL,
  "card_type_code" varchar(1) NOT NULL,
  "account_number" varchar(11) NOT NULL,
  "luhn_digit" varchar(1) NOT NULL,
  "cvv1" varchar(256) NOT NULL,
  "icvv" varchar(256) NOT NULL,
  "cvv2" varchar(256) NOT NULL,
  "track_1" text NOT NULL,
  "track_2" text NOT NULL,
  "application_protocol_data_units" text[] NOT NULL,
  "emv_xml" text NOT NULL,
  "pin_code" varchar(256) NOT NULL,
  "primary_account_number" varchar(16) NOT NULL,
  "expire_year" varchar(2) NOT NULL,
  "expire_month" varchar(2) NOT NULL,
  "meta_data" jsonb NOT NULL DEFAULT ' {}',
  "expires_at" timestamptz,
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "updated_at" timestamptz,
  "deleted_at" timestamptz
);

CREATE TABLE "issuer_identifiers" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "iin" varchar(4) UNIQUE NOT NULL,
  "status" iin_status NOT NULL DEFAULT 'ACTIVE',
  "issuer_user_identifier" varchar(64) UNIQUE NOT NULL,
  "issuer_name" varchar(256) NOT NULL,
  "issuer_logo" varchar(256),
  "issuer_url" varchar(256),
  "des_key" bytea NOT NULL,
  "is_default" boolean NOT NULL DEFAULT false,
  "meta_data" jsonb NOT NULL DEFAULT ' {}',
  "expires_at" timestamptz,
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "updated_at" timestamptz,
  "deleted_at" timestamptz
);

CREATE TABLE "card_types" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "code" varchar(1) UNIQUE NOT NULL,
  "name" varchar(256) NOT NULL,
  "description" varchar(256),
  "meta_data" jsonb NOT NULL DEFAULT ' {}',
  "is_default" boolean NOT NULL DEFAULT false,
  "iin" bigint NOT NULL,
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "updated_at" timestamptz,
  "deleted_at" timestamptz
);

CREATE INDEX ON "cards" ("id");

CREATE INDEX ON "cards" ("identifier");

CREATE INDEX ON "cards" ("user_identifier");

CREATE INDEX ON "cards" ("status");

CREATE INDEX ON "cards" ("iin");

CREATE INDEX ON "cards" ("account_number");

CREATE INDEX ON "cards" ("primary_account_number");

CREATE INDEX ON "cards" ("expire_year");

CREATE INDEX ON "cards" ("expire_month");

CREATE INDEX ON "cards" ("created_at");

CREATE INDEX ON "cards" ("expires_at");

CREATE INDEX ON "cards" ("updated_at");

CREATE INDEX ON "cards" ("deleted_at");

CREATE INDEX ON "cards" ("id", "identifier", "user_identifier");

CREATE INDEX ON "cards" ("id", "identifier", "status");

CREATE INDEX ON "cards" ("id", "identifier", "account_number", "status");

CREATE INDEX ON "cards" ("id", "user_identifier", "status", "primary_account_number", "deleted_at", "expire_year", "expire_month", "expires_at");

CREATE INDEX ON "cards" ("id", "iin", "account_number", "primary_account_number", "status", "expire_year", "expire_month", "expires_at");

CREATE INDEX ON "issuer_identifiers" ("id");

CREATE INDEX ON "issuer_identifiers" ("iin");

CREATE INDEX ON "issuer_identifiers" ("issuer_name");

CREATE INDEX ON "issuer_identifiers" ("created_at");

CREATE INDEX ON "issuer_identifiers" ("updated_at");

CREATE INDEX ON "issuer_identifiers" ("deleted_at");

CREATE INDEX ON "issuer_identifiers" ("id", "identifier", "iin", "issuer_name", "created_at", "updated_at", "deleted_at");

CREATE INDEX ON "card_types" ("id");

CREATE INDEX ON "card_types" ("identifier");

CREATE INDEX ON "card_types" ("name");

CREATE INDEX ON "card_types" ("code");

CREATE INDEX ON "card_types" ("created_at");

CREATE INDEX ON "card_types" ("updated_at");

CREATE INDEX ON "card_types" ("deleted_at");

CREATE INDEX ON "card_types" ("id", "identifier", "code", "name", "created_at", "updated_at", "deleted_at");

COMMENT ON COLUMN "cards"."id" IS 'card internal system unique id';

COMMENT ON COLUMN "cards"."identifier" IS 'unique external identifier for internal-external system identifier isolation';

COMMENT ON COLUMN "cards"."user_identifier" IS 'owner of card external identifier';

COMMENT ON COLUMN "cards"."status" IS 'card status determinable value';

COMMENT ON COLUMN "cards"."iin" IS 'card issuer identifier id';

COMMENT ON COLUMN "cards"."card_type" IS 'card type id';

COMMENT ON COLUMN "cards"."is_user_default_card" IS 'is this card default card for user or not';

COMMENT ON COLUMN "cards"."iin_code" IS 'card issuer identifier code number';

COMMENT ON COLUMN "cards"."card_type_code" IS 'card / service or issuer defined card type code';

COMMENT ON COLUMN "cards"."account_number" IS 'card owner account number';

COMMENT ON COLUMN "cards"."luhn_digit" IS 'Luhn Checksum digit for given (iin + account_number) string';

COMMENT ON COLUMN "cards"."cvv1" IS 'CVV1 for magnetic stripe (service code 101)';

COMMENT ON COLUMN "cards"."icvv" IS 'iCVV for chip cards (service code 201)';

COMMENT ON COLUMN "cards"."cvv2" IS 'CVV2 for card-not-present transactions (service code 999)';

COMMENT ON COLUMN "cards"."track_1" IS 'track 1 data for magnetic stripe cards';

COMMENT ON COLUMN "cards"."track_2" IS 'track 2 data for magnetic stripe cards';

COMMENT ON COLUMN "cards"."application_protocol_data_units" IS 'application protocol data units for chip cards';

COMMENT ON COLUMN "cards"."emv_xml" IS 'EMV profile in XML format';

COMMENT ON COLUMN "cards"."pin_code" IS 'bcrypted random unique 4 or 6 digit pin code number that used for signing transactions';

COMMENT ON COLUMN "cards"."primary_account_number" IS 'complete card number string using [iin(4) + account_number(11) + luhn_checksum(1) = 16 character';

COMMENT ON COLUMN "cards"."expire_year" IS 'issued card valid before this year';

COMMENT ON COLUMN "cards"."expire_month" IS 'issued card valid before this year / month';

COMMENT ON COLUMN "cards"."meta_data" IS 'card metadatas';

COMMENT ON COLUMN "cards"."expires_at" IS 'when the card will expires';

COMMENT ON COLUMN "cards"."created_at" IS 'when card was created';

COMMENT ON COLUMN "cards"."updated_at" IS 'when card was updated';

COMMENT ON COLUMN "cards"."deleted_at" IS 'when card was deleted';

COMMENT ON COLUMN "issuer_identifiers"."id" IS 'iin registry internal system unique id';

COMMENT ON COLUMN "issuer_identifiers"."identifier" IS 'unique external identifier for internal-external system identifier isolation';

COMMENT ON COLUMN "issuer_identifiers"."iin" IS 'card issuer identifier number';

COMMENT ON COLUMN "issuer_identifiers"."status" IS 'iin registry status determinable value';

COMMENT ON COLUMN "issuer_identifiers"."issuer_user_identifier" IS 'issuer owner user external identifier';

COMMENT ON COLUMN "issuer_identifiers"."issuer_name" IS 'issuer name';

COMMENT ON COLUMN "issuer_identifiers"."issuer_logo" IS 'issuer logo url';

COMMENT ON COLUMN "issuer_identifiers"."issuer_url" IS 'issuer website url';

COMMENT ON COLUMN "issuer_identifiers"."des_key" IS '3DES key for encrypting card data';

COMMENT ON COLUMN "issuer_identifiers"."is_default" IS 'is this iin default issuer for creating signed up users default card or not';

COMMENT ON COLUMN "issuer_identifiers"."meta_data" IS 'iin registry metadatas';

COMMENT ON COLUMN "issuer_identifiers"."expires_at" IS 'when the iin registry will expires';

COMMENT ON COLUMN "issuer_identifiers"."created_at" IS 'when iin registry was created';

COMMENT ON COLUMN "issuer_identifiers"."updated_at" IS 'when iin registry was updated';

COMMENT ON COLUMN "issuer_identifiers"."deleted_at" IS 'when iin registry was deleted';

COMMENT ON COLUMN "card_types"."id" IS 'card type internal system unique id';

COMMENT ON COLUMN "card_types"."identifier" IS 'unique external identifier for internal-external system identifier isolation';

COMMENT ON COLUMN "card_types"."code" IS 'card type code';

COMMENT ON COLUMN "card_types"."name" IS 'card type name';

COMMENT ON COLUMN "card_types"."description" IS 'card type description';

COMMENT ON COLUMN "card_types"."meta_data" IS 'card type metadatas';

COMMENT ON COLUMN "card_types"."is_default" IS 'is this card type default type for creating signed up users default card or not';

COMMENT ON COLUMN "card_types"."iin" IS 'card issuer identifier id';

COMMENT ON COLUMN "card_types"."created_at" IS 'when card type was created';

COMMENT ON COLUMN "card_types"."updated_at" IS 'when card type was updated';

COMMENT ON COLUMN "card_types"."deleted_at" IS 'when card type was deleted';

ALTER TABLE "cards" ADD FOREIGN KEY ("iin") REFERENCES "issuer_identifiers" ("id");

ALTER TABLE "cards" ADD FOREIGN KEY ("card_type") REFERENCES "card_types" ("id");

ALTER TABLE "card_types" ADD FOREIGN KEY ("iin") REFERENCES "issuer_identifiers" ("id");












CREATE TYPE "workflow_type" AS ENUM (
  'NUMSCRIPT',
  'WORKFLOW_YAML'
);

CREATE TYPE "transaction_draft_type" AS ENUM (
  'MOBILE',
  'NATIONAL_ID',
  'CARD_NUMBER'
);

CREATE TABLE "transaction_drafts" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "draft_type" transaction_draft_type NOT NULL DEFAULT 'MOBILE',
  "totp" varchar(256) UNIQUE NOT NULL,
  "reference_id" varchar(256) UNIQUE NOT NULL,
  "meta_data" jsonb NOT NULL DEFAULT '{}',
  "card_number" varchar(19) NOT NULL,
  "source_account" varchar(512) NOT NULL,
  "destination_account" varchar(512) NOT NULL,
  "asset" varchar(8) NOT NULL,
  "amount" float NOT NULL,
  "expires_at" timestamptz,
  "updated_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP)
);

CREATE TABLE "workflows" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "slug" varchar(256) UNIQUE NOT NULL,
  "description" varchar(256) NOT NULL,
  "is_active" boolean NOT NULL DEFAULT 'false',
  "allowed" boolean NOT NULL DEFAULT 'true',
  "workflow_id" varchar(256) NOT NULL,
  "workflow_type" workflow_type NOT NULL DEFAULT 'NUMSCRIPT',
  "source_ledger" varchar(256) NOT NULL DEFAULT 'default',
  "source_address_pattern" varchar(256) NOT NULL,
  "destination_address_pattern" varchar(256) NOT NULL,
  "priority_override" integer NOT NULL DEFAULT **********,
  "asset" varchar(8) NOT NULL,
  "up_amount_threshold" float NOT NULL DEFAULT 0,
  "down_amount_threshold" float NOT NULL DEFAULT 0,
  "meta_data" jsonb NOT NULL DEFAULT '{}',
  "workflow_data" bytea NOT NULL,
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "updated_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "deleted_at" timestamptz
);

CREATE INDEX ON "transaction_drafts" ("id");

CREATE INDEX ON "transaction_drafts" ("identifier");

CREATE INDEX ON "transaction_drafts" ("draft_type");

CREATE INDEX ON "transaction_drafts" ("expires_at");

CREATE INDEX ON "transaction_drafts" ("updated_at");

CREATE INDEX ON "transaction_drafts" ("identifier", "expires_at", "updated_at");

CREATE INDEX ON "workflows" ("id");

CREATE INDEX ON "workflows" ("identifier");

CREATE INDEX ON "workflows" ("slug");

CREATE INDEX ON "workflows" ("is_active");

CREATE INDEX ON "workflows" ("created_at");

CREATE INDEX ON "workflows" ("updated_at");

COMMENT ON COLUMN "transaction_drafts"."id" IS 'draft internal system unique id';

COMMENT ON COLUMN "transaction_drafts"."identifier" IS 'unique external identifier for internal-external system identifier isolation';

COMMENT ON COLUMN "transaction_drafts"."draft_type" IS 'represent type of draft transaction';

COMMENT ON COLUMN "transaction_drafts"."totp" IS 'bcrypted TOTP code for validating transaction during committing it';

COMMENT ON COLUMN "transaction_drafts"."reference_id" IS 'idempotency key for making create draft transaction idempotent';

COMMENT ON COLUMN "transaction_drafts"."meta_data" IS 'transaction metadatas';

COMMENT ON COLUMN "transaction_drafts"."card_number" IS 'card number for card number draft type';

COMMENT ON COLUMN "transaction_drafts"."source_account" IS 'source account address for payer of the payment';

COMMENT ON COLUMN "transaction_drafts"."destination_account" IS 'destination account address for acceptor of the payment';

COMMENT ON COLUMN "transaction_drafts"."asset" IS 'asset for transaction flow to match with';

COMMENT ON COLUMN "transaction_drafts"."amount" IS 'amount for transaction flow to match with';

COMMENT ON COLUMN "transaction_drafts"."expires_at" IS 'when the draft will expires';

COMMENT ON COLUMN "transaction_drafts"."updated_at" IS 'when draft was updated';

COMMENT ON COLUMN "transaction_drafts"."created_at" IS 'when draft was created';

COMMENT ON COLUMN "workflows"."id" IS 'transaction flow internal system unique id';

COMMENT ON COLUMN "workflows"."identifier" IS 'unique external identifier for internal-external system identifier isolation';

COMMENT ON COLUMN "workflows"."slug" IS 'unique slug for transaction flow';

COMMENT ON COLUMN "workflows"."description" IS 'description for transaction flow';

COMMENT ON COLUMN "workflows"."is_active" IS 'is transaction flow active';

COMMENT ON COLUMN "workflows"."allowed" IS 'allow or disallow transaction route';

COMMENT ON COLUMN "workflows"."workflow_id" IS 'formance workflow id for transaction flow';

COMMENT ON COLUMN "workflows"."workflow_type" IS 'type of workflow';

COMMENT ON COLUMN "workflows"."source_ledger" IS 'formance ledger id for transaction flow to separate transaction flows between different ledgers';

COMMENT ON COLUMN "workflows"."source_address_pattern" IS 'source account address pattern that contains "Domain Segments" for payer to match with';

COMMENT ON COLUMN "workflows"."destination_address_pattern" IS 'destination account address pattern that contains "Domain Segments" for acceptor of the payment to match with';

COMMENT ON COLUMN "workflows"."priority_override" IS 'priority override for transaction flow';

COMMENT ON COLUMN "workflows"."asset" IS 'asset for transaction flow to match with';

COMMENT ON COLUMN "workflows"."up_amount_threshold" IS 'maximum amount for transaction flow to execute';

COMMENT ON COLUMN "workflows"."down_amount_threshold" IS 'minimum amount for transaction flow to execute';

COMMENT ON COLUMN "workflows"."meta_data" IS 'transaction flow metadatas';

COMMENT ON COLUMN "workflows"."workflow_data" IS 'workflow binary data for transaction flow execution, it can contains Numscript script or Formance Orchestration workflow yaml file content';

COMMENT ON COLUMN "workflows"."created_at" IS 'when transaction flow was created';

COMMENT ON COLUMN "workflows"."updated_at" IS 'when transaction flow was updated';

COMMENT ON COLUMN "workflows"."deleted_at" IS 'when transaction flow was deleted';