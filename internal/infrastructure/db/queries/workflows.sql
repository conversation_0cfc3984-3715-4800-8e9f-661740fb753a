-- name: FindRegexMatchesWorkflows :many
SELECT *
FROM workflows
WHERE REGEX_MATCHES(
        $1,
        '^' || REPLACE(source_domain_pattern, '*', '[^:]*') || '$'
    ) IS NOT NULL
    AND REGEX_MATCHES(
        $2,
        '^' || REPLACE(destination_domain_pattern, '*', '[^:]*') || '$'
    ) IS NOT NULL
    AND down_amount_threshold <= $3
    AND up_amount_threshold >= $4
    AND is_active = true
    AND deleted_at IS NULL;
    
-- name: FindMatchesWorkflows :many
SELECT *
FROM workflows
WHERE $1 LIKE REPLACE(source_address_pattern, '*', '%')
    AND $2 LIKE REPLACE(destination_address_pattern, '*', '%')
    AND down_amount_threshold <= $3
    AND up_amount_threshold >= $4
    AND is_active = true
    AND deleted_at IS NULL;

-- name: SelectAllActiveWorkflows :many
SELECT *
FROM workflows
WHERE is_active IS TRUE
    AND deleted_at IS NOT NULL
ORDER BY priority_override ASC;