-- name: GetDraftTransactionByIdentifier :one
SELECT *
FROM transaction_drafts
WHERE identifier = $1
    AND expires_at > 'now()'
LIMIT 1;

-- name: GetDraftTransactionByIdentifierAndReference :one
SELECT *
FROM transaction_drafts
WHERE identifier = $1
    AND reference_id = $2
    AND expires_at > 'now()'
LIMIT 1;

-- name: UpsertTransaction :one
INSERT INTO transaction_drafts (
        identifier,
        draft_type,
        totp,
        reference_id,
        card_number,
        source_account,
        destination_account,
        asset,
        amount,
        meta_data,
        expires_at,
        updated_at
    )
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 'now()')
RETURNING *;