Project transaction_service_schema {
  database_type: 'PostgreSQL'
  Note: '''
    # Transaction Service Database Schema
    # Transaction Service is a microservice that provides transaction related operations.
    # It is responsible for processing transaction and also managing transaction flows and related operations.
    # It uses PostgreSQL as a database by default.
  '''
}

// transaction_draft_type enum for defining type of draft transaction
// transaction_draft_type is used to determine the type of payment draft request.
// It includes various types such as mobile, national id, and card number (extendible in future).
// Each type has a specific meaning and is used to determine the source of payment draft request according to user request.
// The statuses are defined as follows:
Enum transaction_draft_type {
    MOBILE [note: 'payment draft created using mobile number']
    NATIONAL_ID [note: 'payment draft created using national id']
    CARD_NUMBER [note: 'payment draft created using card number']
}

// transaction_drafts table is temporary transaction buffer for storing cardless transaction request properties.
// It stores all the necessary information about cardless transaction's type, validity time, , and metadata.
// The table structure is as follows:
Table transaction_drafts {
  id bigserial [pk, note: 'draft internal system unique id']
  identifier varchar(64) [unique, not null, note: 'unique external identifier for internal-external system identifier isolation']
  draft_type transaction_draft_type [not null, default:'MOBILE', note: 'represent type of draft transaction']
  totp varchar(256) [not null, unique, note: 'bcrypted TOTP code for validating transaction during committing it']
  reference_id varchar(256) [not null, unique, note: 'idempotency key for making create draft transaction idempotent']
  meta_data jsonb [not null, default: '{}', note: 'transaction metadatas']

  card_number varchar(19) [not null, note: 'card number for card number draft type']
  source_account varchar(512) [not null, note: 'source account address for payer of the payment']
  destination_account varchar(512) [not null, note: 'destination account address for acceptor of the payment']
  asset varchar(8) [not null, note: 'asset for transaction flow to match with']
  amount float [not null, note: 'amount for transaction flow to match with']
  
  expires_at timestamptz [note: 'when the draft will expires']
  updated_at timestamptz [not null, default: `CURRENT_TIMESTAMP`, note: 'when draft was updated']
  created_at timestamptz [not null, default: `CURRENT_TIMESTAMP`, note: 'when draft was created']

  Indexes {
    id
    identifier
    draft_type
    expires_at
    updated_at
    (identifier, expires_at, updated_at)
  }
}

// workflow_type enum for defining type of workflow
// NUMSCRIPT: workflow is defined using numscript
// WORKFLOW_YAML: workflow is defined using workflow yaml
Enum workflow_type {
    NUMSCRIPT [note: 'workflow is defined using numscript']
    WORKFLOW_YAML [note: 'workflow is defined using workflow yaml']
}

// workflows table is used to store transaction flow properties.
// It stores all the necessary information about transaction flow's type, slug, description, and metadata.
// The table structure is as follows:
// source_domain_pattern and destination_domain_pattern are used to match with source and destination account address for transaction flow execution.
// Example ***********
// based on default naming convention for accounts that provided by formance
// accounts have pattern look like `user_325:iin:card_type:account_number:contracts:contract_id`
// for example we have source account based on above pattern as below
// 325:0:1:**************:contracts:**************
// so the source_domain_pattern will be `*:0:1:*:contracts:*`
// in another side we have a merchant that would accept payment
// and the account for that merchant is `420:0:2:**************:merchant:contracts:**************`
// and the destination_domain_pattern will be `*:0:2:*:contracts:*`
// now we can match source and destination account address with source_domain_pattern and destination_domain_pattern
// and execute the transaction flow for that specific transaction
Table workflows {
  id bigserial [pk, note: 'transaction flow internal system unique id']
  identifier varchar(64) [unique, not null, note: 'unique external identifier for internal-external system identifier isolation']
  slug varchar(256) [unique, not null, note: 'unique slug for transaction flow']
  description varchar(256) [not null, note: 'description for transaction flow']
  is_active boolean [not null, default: 'false', note: 'is transaction flow active']

  allowed boolean [not null, default: 'true', note: 'allow or disallow transaction route']

  workflow_id varchar(256) [not null, note: 'formance workflow id for transaction flow']
  workflow_type workflow_type [not null, default: 'NUMSCRIPT', note: 'type of workflow']

  source_ledger varchar(256) [not null, default: 'default', note: 'formance ledger id for transaction flow to separate transaction flows between different ledgers']
  source_address_pattern varchar(256) [not null, note: 'source account address pattern that contains "Domain Segments" for payer to match with']
  destination_address_pattern varchar(256) [not null, note: 'destination account address pattern that contains "Domain Segments" for acceptor of the payment to match with']
  priority_override integer [not null, default: **********, note: 'priority override for transaction flow']
  
  asset varchar(8) [not null, note: 'asset for transaction flow to match with']
  up_amount_threshold float [not null, default: 0, note: 'maximum amount for transaction flow to execute']
  down_amount_threshold float [not null, default: 0, note: 'minimum amount for transaction flow to execute']
  
  meta_data jsonb [not null, default: '{}', note: 'transaction flow metadatas']

  workflow_data bytea [not null, note: 'workflow binary data for transaction flow execution, it can contains Numscript script or Formance Orchestration workflow yaml file content']

  created_at timestamptz [not null, default: `CURRENT_TIMESTAMP`, note: 'when transaction flow was created']
  updated_at timestamptz [not null, default: `CURRENT_TIMESTAMP`, note: 'when transaction flow was updated']
  deleted_at timestamptz [note: 'when transaction flow was deleted']

  Indexes {
    id
    identifier
    slug
    is_active
    created_at
    updated_at
  }
}