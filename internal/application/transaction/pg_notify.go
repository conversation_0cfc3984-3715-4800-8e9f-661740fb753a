package transaction

import (
	"context"
	"log"
)

// ListenWorkflowChanges listens to PostgreSQL NOTIFY messages
// on 'workflow_channel' and calls the reload function.
func (s *service) ListenWorkflowChanges(ctx context.Context, reloadFunc func()) error {
	conn, err := s.poll.Acquire(ctx)
	if err != nil {
		return err
	}
	_, err = conn.Exec(ctx, "LISTEN workflow_channel")
	if err != nil {
		return err
	}

	go func() {
		for {
			notification, err := conn.Conn().WaitForNotification(ctx)
			if err != nil {
				log.Printf("error waiting for workflow notification: %v", err)
				continue
			}
			if notification != nil && notification.Payload == "update" {
				log.Println("detected workflow change, reloading...")
				reloadFunc()
			}
		}
	}()

	return nil
}
