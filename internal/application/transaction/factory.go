package transaction

import (
	formancesdkgo "github.com/formancehq/formance-sdk-go/v3"
	kitlog "github.com/go-kit/log"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/go-lib/servicemesh"
	"github.com/liveutil/transaction_service/internal/config"
	"github.com/liveutil/transaction_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/transaction_service/pb"
	"github.com/nats-io/nats.go"
	"github.com/redis/go-redis/v9"
	"go.opentelemetry.io/otel/trace"
)

type TransactionServiceOpts struct {
	Repository       postgres.Store
	PostgresPoll     *pgxpool.Pool
	Config           *config.Configuration
	Redis            *redis.Client
	NATS             *nats.Conn
	UsersServiceMesh servicemesh.UserMeshService
	CardServiceMesh  servicemesh.CardMeshService
	Logger           kitlog.Logger
	PASETO           paseto.Maker
	SchemaPath       string
	ApplicationName  string
	Tracer           trace.Tracer
	FormanceClient   *formancesdkgo.Formance
}

// NewTransactionService creates a new user service with all middleware layers
func NewTransactionService(opts *TransactionServiceOpts) (pb.TransactionServiceServer, error) {
	// Create base service
	svc := NewService(opts)

	// Add middleware layers
	svc = NewAuthorizationMiddleware(svc)

	return svc, nil
}
