package transaction

type TransactionRequest struct {
	SourceAccount      string            `json:"source_account"`
	DestinationAccount string            `json:"destination_account"`
	Asset              string            `json:"asset"`
	ReferenceID        string            `json:"reference_id"`
	Metadata           map[string]string `json:"metadata"`
	Amount             float64           `json:"amount"`
}

type TransactionResponse struct {
}
