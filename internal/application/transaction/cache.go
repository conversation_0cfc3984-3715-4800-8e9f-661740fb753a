package transaction

import "sync"

// localWorkflowCache stores the active list of workflows in memory
// using sync.Map for safe concurrent access.
var localWorkflowCache sync.Map

// CacheWorkflows stores a list of workflows in the local cache.
func CacheWorkflows(workflows []PreloadedWorkflow) {
	localWorkflowCache.Store("active", workflows)
}

// GetCachedWorkflows retrieves the cached list of workflows.
func GetCachedWorkflows() []PreloadedWorkflow {
	if val, ok := localWorkflowCache.Load("active"); ok {
		return val.([]PreloadedWorkflow)
	}
	return nil
}
