package transaction

import (
	"context"
	"encoding/json"
	"fmt"
	// Redis client used for caching and pubsub
)

// LoadWorkflowsFromRedis fetches a JSON-encoded workflow list from Redis,
// parses it into Workflow structs, and pre-splits the patterns.
func (s *service) LoadWorkflowsFromRedis(ctx context.Context) ([]PreloadedWorkflow, error) {
	data, err := s.redis.Get(ctx, "workflows:active").Bytes()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch workflows from redis: %w", err)
	}

	var workflows []PreloadedWorkflow
	if err := json.Unmarshal(data, &workflows); err != nil {
		return nil, fmt.Errorf("failed to unmarshal workflows: %w", err)
	}

	return parsePatterns(workflows), nil
}

// WatchWorkflowReload subscribes to a Redis pubsub channel.
// On receiving "update", it calls the reloadFunc.
func (s *service) WatchWorkflowReload(ctx context.Context, reloadFunc func()) {
	pubsub := s.redis.Subscribe(ctx, "workflow:reload")
	ch := pubsub.Channel()
	go func() {
		for msg := range ch {
			if msg.Payload == "update" {
				reloadFunc()
			}
		}
	}()
}
