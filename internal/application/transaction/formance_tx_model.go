package transaction

import "time"

// FormanceTransaction represents a transaction from Formance ledger
type FormanceTransaction struct {
	Data FormanceTransactionData `json:"data"`
}

// FormanceTransactionData contains the transaction details
type FormanceTransactionData struct {
	WorkflowID                 string                       `json:"workflowID"`
	InsertedAt                 time.Time                    `json:"insertedAt"`
	Timestamp                  time.Time                    `json:"timestamp"`
	Postings                   []FormancePosting            `json:"postings"`
	Reference                  string                       `json:"reference"`
	Metadata                   map[string]string            `json:"metadata"`
	ID                         int64                        `json:"id"`
	Reverted                   bool                         `json:"reverted"`
	RevertedAt                 time.Time                    `json:"revertedAt"`
	PreCommitVolumes           map[string]map[string]Volume `json:"preCommitVolumes"`
	PostCommitVolumes          map[string]map[string]Volume `json:"postCommitVolumes"`
	PreCommitEffectiveVolumes  map[string]map[string]Volume `json:"preCommitEffectiveVolumes"`
	PostCommitEffectiveVolumes map[string]map[string]Volume `json:"postCommitEffectiveVolumes"`
}

// FormancePosting represents a single posting in a transaction
type FormancePosting struct {
	Amount      int64  `json:"amount"`
	Asset       string `json:"asset"`
	Destination string `json:"destination"`
	Source      string `json:"source"`
}

// Volume represents account volume information
type Volume struct {
	Input   int64 `json:"input"`
	Output  int64 `json:"output"`
	Balance int64 `json:"balance"`
}
