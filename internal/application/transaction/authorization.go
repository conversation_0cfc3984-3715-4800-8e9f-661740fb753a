package transaction

import (
	"context"

	"github.com/liveutil/transaction_service/pb"
)

type authorizationMiddleware struct {
	pb.UnimplementedTransactionServiceServer
	// next is the next service in the middleware chain
	next pb.TransactionServiceServer
}

// NewAuthorizationMiddleware returns new authorization layer for pb.TransactionServiceServer
func NewAuthorizationMiddleware(service pb.TransactionServiceServer) pb.TransactionServiceServer {
	return &authorizationMiddleware{
		next: service,
	}
}

// FetchUserCardsForDraftTransaction implements pb.TransactionServiceServer.
func (a *authorizationMiddleware) FetchUserCardsForDraftTransaction(ctx context.Context, req *pb.DraftTransactionRequest) (*pb.FetchUserCardsListForDraftTransactionResponse, error) {
	return a.next.FetchUserCardsForDraftTransaction(ctx, req)
}

// CommitDraftTransaction implements pb.TransactionServiceServer.
func (a *authorizationMiddleware) CommitDraftTransaction(ctx context.Context, req *pb.CommitDraftTransactionRequest) (*pb.CommitDraftTransactionResponse, error) {
	return a.next.CommitDraftTransaction(ctx, req)
}

// CommitTransaction implements pb.TransactionServiceServer.
func (a *authorizationMiddleware) CommitTransaction(ctx context.Context, req *pb.CommitTransactionRequest) (*pb.CommitTransactionResponse, error) {
	return a.next.CommitTransaction(ctx, req)
}

// DraftTransaction implements pb.TransactionServiceServer.
func (a *authorizationMiddleware) DraftTransaction(ctx context.Context, req *pb.DraftTransactionRequest) (*pb.DraftTransactionResponse, error) {
	return a.next.DraftTransaction(ctx, req)
}
