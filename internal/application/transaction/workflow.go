package transaction

import (
	"context"
	"errors"
	"strings"

	"github.com/liveutil/transaction_service/internal/infrastructure/db/postgres"
)

const (
	// DefaultWorkflowPriority is the default priority for a workflow.
	DefaultWorkflowPriority = 2147483647
)

type PreloadedWorkflow struct {
	postgres.Workflow
	SrcSegments []string `json:"src_segments"`
	DstSegments []string `json:"dst_segments"`
}

// splitAddress splits a colon-separated address or pattern string into parts.
func splitAddress(addr string) []string {
	return strings.Split(addr, ":")
}

// parsePatterns prepares workflows by splitting their source and destination patterns.
func parsePatterns(workflows []PreloadedWorkflow) []PreloadedWorkflow {
	for i := range workflows {
		workflows[i].SrcSegments = splitAddress(workflows[i].SourceAddressPattern)
		workflows[i].DstSegments = splitAddress(workflows[i].DestinationAddressPattern)
	}
	return workflows
}

func (s *service) QueryWorkflow(ctx context.Context, tx TransactionRequest) (*postgres.Workflow, error) {
	workflows, err := s.repo.FindMatchesWorkflows(ctx, postgres.FindMatchesWorkflowsParams{
		SourceAddressPattern:      tx.SourceAccount,
		DestinationAddressPattern: tx.DestinationAccount,
	})
	if err != nil {
		return nil, err
	}

	workflowList, err := WeightedWorkflows(tx, workflows)
	if err != nil {
		return nil, err
	}

	if workflow, ok := workflowList[0]; ok {
		return &workflow, nil
	}

	return nil, errors.New("no matched workflow found")
}

// @ToDo: implement algorithm to give weight to each workflow and select the best one
func WeightedWorkflows(tx TransactionRequest, workflows []postgres.Workflow) (map[int]postgres.Workflow, error) {
	list := make(map[int]postgres.Workflow)

	for index, workflow := range workflows {
		list[index] = workflow
	}

	return list, nil
}

func (s *service) StartupPreloadWorkflows(ctx context.Context) error {
	workflows, err := s.repo.SelectAllActiveWorkflows(ctx)
	if err != nil {
		return err
	}

	preloadedWorkflows := make([]PreloadedWorkflow, len(workflows))
	for i, workflow := range workflows {
		preloadedWorkflows[i] = PreloadedWorkflow{Workflow: workflow}
	}

	CacheWorkflows(parsePatterns(preloadedWorkflows))

	return nil
}
