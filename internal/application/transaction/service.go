package transaction

import (
	"context"
	"encoding/json"
	"fmt"
	"math/big"
	"time"

	formancesdkgo "github.com/formancehq/formance-sdk-go/v3"
	"github.com/formancehq/formance-sdk-go/v3/pkg/models/operations"
	"github.com/formancehq/formance-sdk-go/v3/pkg/models/shared"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/go-lib/servicemesh"
	"github.com/liveutil/go-lib/totp"
	"github.com/liveutil/go-lib/worker"
	"github.com/liveutil/transaction_service/internal/config"
	"github.com/liveutil/transaction_service/internal/domain/formancetx"
	"github.com/liveutil/transaction_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/transaction_service/pb"
	"github.com/nats-io/nats.go"
	"github.com/oklog/ulid/v2"
	"github.com/pquerna/otp"
	"github.com/redis/go-redis/v9"
	"go.opentelemetry.io/otel/trace"
	"golang.org/x/crypto/bcrypt"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type service struct {
	pb.UnimplementedTransactionServiceServer

	repo         postgres.Store
	poll         *pgxpool.Pool
	redis        *redis.Client
	paseto       paseto.Maker
	config       *config.Configuration
	users        servicemesh.UserMeshService
	cards        servicemesh.CardMeshService
	tracer       trace.Tracer
	totp_config  totp.TOTPServiceConfig
	totp_service totp.TOTPService
	nats         *nats.Conn
	formance     *formancesdkgo.Formance
}

// CardServiceOpts defines options for creating a new CardService.
func NewService(opts *TransactionServiceOpts) pb.TransactionServiceServer {
	totpConfig := totp.TOTPServiceConfig{
		Issuer:    opts.Config.Issuer,
		Digits:    6,
		Period:    300,
		Algorithm: otp.AlgorithmSHA1,
		Skew:      1,
	}

	return &service{
		repo:         opts.Repository,
		poll:         opts.PostgresPoll,
		config:       opts.Config,
		paseto:       opts.PASETO,
		users:        opts.UsersServiceMesh,
		cards:        opts.CardServiceMesh,
		tracer:       opts.Tracer,
		totp_config:  totpConfig,
		totp_service: totp.NewRedisTOTPService(opts.Redis, opts.ApplicationName, totpConfig),
		nats:         opts.NATS,
		formance:     opts.FormanceClient,
	}
}

// CommitTransaction implements pb.TransactionServiceServer.
func (s *service) CommitTransaction(ctx context.Context, req *pb.CommitTransactionRequest) (*pb.CommitTransactionResponse, error) {
	// workflow, err := s.QueryWorkflow(tx, TransactionRequest{
	// 	SourceAccount:      req.SourceAccount,
	// 	DestinationAccount: req.DestinationAccount,
	// 	Asset:              req.Asset,
	// 	Amount:             req.Amount,
	// 	ReferenceID:        req.ReferenceId,
	// 	Metadata:           req.Metadata,
	// })
	// if err != nil {
	// 	return nil, err
	// }

	// // @ToDo: implement transaction flow execution based on workflow type and its data to 'Formance Flows'
	// if workflow.WorkflowType == postgres.WorkflowTypeNUMSCRIPT {
	// 	// execute numscript
	// } else if workflow.WorkflowType == postgres.WorkflowTypeWORKFLOWYAML {
	// 	// execute formance workflow
	// }

	// card, err := s.repo.GetCardByPAN(ctx, req.CardNumber)
	card, err := s.cards.GetSafeCardByCardNumber(ctx, req.CardNumber)
	if err != nil {
		return nil, err
	}

	// check if card is active
	if card.Status != "ACTIVE" {
		return nil, status.Errorf(codes.InvalidArgument, "card is not active")
	}

	// check if card is expired
	if card.ExpiresAt != nil {
		if card.ExpiresAt.Before(time.Now().UTC()) {
			return nil, status.Errorf(codes.InvalidArgument, "card is expired")
		}
	}

	account := fmt.Sprintf(s.config.DefaultCardCreditAccountPattern,
		card.UserIdentifier,
		card.IinCode,
		card.CardTypeCode,
		card.AccountNumber,
		card.LuhnDigit,
	)

	// create a multiple posting transaction s.config.DefaultCardCreditAccountPattern)

	fee := int64(req.Amount) * 1 / 100
	discount := int64(req.Amount) * 3 / 100
	commission := int64(req.Amount) * 2 / 100

	// create a multiple posting transaction
	tx := operations.V2CreateTransactionRequest{
		V2PostTransaction: shared.V2PostTransaction{
			Metadata: map[string]string{
				"terminal_id": "demo_terminal_id",
				"signing_key": "demo_signing_key",
				"invoice_id":  "demo_invoice_id",
				"description": req.Description,
			},
			Postings: []shared.V2Posting{
				{
					Amount:      big.NewInt(int64(req.Amount)),
					Asset:       req.Asset,
					Source:      account,
					Destination: req.DestinationAccount,
				},
				{
					Amount:      big.NewInt(fee),
					Asset:       req.Asset,
					Source:      req.DestinationAccount,
					Destination: "salam_pay:fee:default",
				},
				{
					Amount:      big.NewInt(discount),
					Asset:       req.Asset,
					Source:      "world",
					Destination: "salam_pay:discount:default",
				},
				{
					Amount:      big.NewInt(discount),
					Asset:       req.Asset,
					Source:      "salam_pay:discount:default",
					Destination: account,
				},
				{
					Amount:      big.NewInt(commission),
					Asset:       req.Asset,
					Source:      req.DestinationAccount,
					Destination: fmt.Sprintf("%s:%s:commission:default", card.IinCode, card.CardTypeCode),
				},
			},
			Reference: formancesdkgo.String(req.ReferenceId),
		},
		DryRun: formancesdkgo.Bool(false),
		Force:  formancesdkgo.Bool(false),
		Ledger: s.config.DefaultNetworkCreditLedger,
	}

	txRes, err := formancetx.CreateTransaction(ctx, s.formance, tx)
	if err != nil {
		return nil, fmt.Errorf("create transaction error: %v", err)
	}

	return &pb.CommitTransactionResponse{
		Error:   false,
		Message: "transaction committed successfully",
		Transaction: &pb.Transaction{
			SourceAccount:      account,
			DestinationAccount: req.DestinationAccount,
			ReferenceId:        *txRes.GetV2CreateTransactionResponse().GetData().Reference,
			Amount:             req.Amount,
			Asset:              req.Asset,
			Description:        req.Description,
			TracingId:          txRes.GetV2CreateTransactionResponse().GetData().ID.String(),
			Metadata: map[string]string{
				"terminal_id": "demo_terminal_id",
				"signing_key": "demo_signing_key",
				"invoice_id":  "demo_invoice_id",
				"created_at":  txRes.GetV2CreateTransactionResponse().GetData().InsertedAt.Format(time.RFC3339),
			},
		},
	}, nil
}

// CommitDraftTransaction implements pb.TransactionServiceServer.
func (s *service) CommitDraftTransaction(ctx context.Context, req *pb.CommitDraftTransactionRequest) (*pb.CommitDraftTransactionResponse, error) {
	// draft, err := s.repo.GetDraftTransactionById(ctx, req.PaymentDraftId)
	// if err != nil {
	// 	return nil, err
	// }

	// // validate totp code
	// err = bcrypt.CompareHashAndPassword([]byte(draft.Totp), []byte(req.Totp))
	// if err != nil {
	// 	return nil, err
	// }

	// metadata := make(map[string]string)
	// err = json.Unmarshal(draft.MetaData, &metadata)
	// if err != nil {
	// 	return nil, err
	// }

	// // @ToDo: implement transaction flow execution based on workflow type and its data to 'Formance Flows'
	// workflow, err := s.QueryWorkflow(ctx, TransactionRequest{
	// 	SourceAccount:      draft.SourceAccount,
	// 	DestinationAccount: draft.DestinationAccount,
	// 	Asset:              draft.Asset,
	// 	Amount:             draft.Amount,
	// 	ReferenceID:        draft.ReferenceID,
	// 	Metadata:           metadata,
	// })

	// if err != nil {
	// 	return nil, err
	// }

	// // @ToDo: implement transaction flow execution based on workflow type and its data to 'Formance Flows'
	// if workflow.WorkflowType == postgres.WorkflowTypeNUMSCRIPT {
	// 	// execute numscript
	// } else if workflow.WorkflowType == postgres.WorkflowTypeWORKFLOWYAML {
	// 	// execute formance workflow
	// }

	// return &pb.CommitDraftTransactionResponse{}, nil

	draft, err := s.repo.GetDraftTransactionByIdentifierAndReference(ctx, postgres.GetDraftTransactionByIdentifierAndReferenceParams{
		Identifier:  req.PaymentDraftIdentifier,
		ReferenceID: req.ReferenceId,
	})
	if err != nil {
		return nil, err
	}

	// validate totp code
	err = bcrypt.CompareHashAndPassword([]byte(draft.Totp), []byte(req.Totp))
	if err != nil {
		return nil, err
	}

	// find card info from card service mesh based on card number
	card, err := s.cards.GetSafeCardByCardNumber(ctx, draft.CardNumber)
	if err != nil {
		return nil, err
	}

	issuer, err := s.cards.GetSafeIssuerByIIN(ctx, card.IinCode)
	if err != nil {
		return nil, err
	}

	// check if card is active
	if card.Status != "ACTIVE" {
		return nil, status.Errorf(codes.InvalidArgument, "card is not active")
	}

	// check if card is expired
	if card.ExpiresAt != nil {
		if card.ExpiresAt.Before(time.Now().UTC()) {
			return nil, status.Errorf(codes.InvalidArgument, "card is expired")
		}
	}

	account := fmt.Sprintf(s.config.DefaultCardCreditAccountPattern,
		card.UserIdentifier,
		card.IinCode,
		card.CardTypeCode,
		card.AccountNumber,
		card.LuhnDigit,
	)

	// create a multiple posting transaction s.config.DefaultCardCreditAccountPattern)
	fee := (int64(draft.Amount) * 1 / 100) + 2000
	discount := int64(draft.Amount) * 3 / 100
	commission := int64(draft.Amount) * 2 / 100

	// create a multiple posting transaction
	tx := operations.V2CreateTransactionRequest{
		V2PostTransaction: shared.V2PostTransaction{
			Metadata: map[string]string{
				"terminal_id": "demo_terminal_id",
				"signing_key": "demo_signing_key",
				"invoice_id":  "demo_invoice_id",
				"description": req.Description,
			},
			Postings: []shared.V2Posting{
				{
					Amount:      big.NewInt(int64(draft.Amount)),
					Asset:       draft.Asset,
					Source:      account,
					Destination: draft.DestinationAccount,
				},
				{
					Amount:      big.NewInt(fee),
					Asset:       draft.Asset,
					Source:      draft.DestinationAccount,
					Destination: "salam_pay:fee:default",
				},
				{
					Amount:      big.NewInt(discount),
					Asset:       draft.Asset,
					Source:      "world",
					Destination: "salam_pay:discount:default",
				},
				{
					Amount:      big.NewInt(discount),
					Asset:       draft.Asset,
					Source:      "salam_pay:discount:default",
					Destination: account,
				},
				{
					Amount:      big.NewInt(commission),
					Asset:       draft.Asset,
					Source:      draft.DestinationAccount,
					Destination: fmt.Sprintf("%s:%s:commission:default", issuer.Iin, card.CardTypeCode),
				},
			},
			Reference: formancesdkgo.String(req.ReferenceId),
		},
		DryRun: formancesdkgo.Bool(false),
		Force:  formancesdkgo.Bool(false),
		Ledger: s.config.DefaultNetworkCreditLedger,
	}

	txRes, err := formancetx.CreateTransaction(ctx, s.formance, tx)
	if err != nil {
		return nil, fmt.Errorf("create transaction error: %v", err)
	}

	return &pb.CommitDraftTransactionResponse{
		Error:   false,
		Message: "transaction committed successfully",
		Transaction: &pb.Transaction{
			SourceAccount:      account,
			DestinationAccount: draft.DestinationAccount,
			ReferenceId:        *txRes.GetV2CreateTransactionResponse().GetData().Reference,
			Amount:             draft.Amount,
			Asset:              draft.Asset,
			Description:        req.Description,
			TracingId:          txRes.GetV2CreateTransactionResponse().GetData().ID.String(),
			Metadata: map[string]string{
				"terminal_id": "demo_terminal_id",
				"signing_key": "demo_signing_key",
				"invoice_id":  "demo_invoice_id",
				"created_at":  txRes.GetV2CreateTransactionResponse().GetData().InsertedAt.Format(time.RFC3339),
			},
		},
	}, nil
}

// FetchUserCardsForDraftTransaction implements pb.TransactionServiceServer.
func (s *service) FetchUserCardsForDraftTransaction(ctx context.Context, req *pb.DraftTransactionRequest) (*pb.FetchUserCardsListForDraftTransactionResponse, error) {
	// define payer user model to fill based on identifier type
	var payer *servicemesh.UserModel
	var err error

	// get payer user model based on identifier type
	switch req.DraftType {
	case string(postgres.TransactionDraftTypeMOBILE):
	case string(postgres.TransactionDraftTypeNATIONALID):
		payer, err = s.users.GetSafeUserByIdentifier(ctx, req.PayerIdentifier)
		if err != nil {
			return nil, err
		}
	case string(postgres.TransactionDraftTypeCARDNUMBER):
		card, err := s.cards.GetSafeCardByCardNumber(ctx, req.PayerIdentifier)
		if err != nil {
			return nil, err
		}

		payer, err = s.users.GetSafeUserByIdentifier(ctx, card.UserIdentifier)
		if err != nil {
			return nil, err
		}
	}

	// get cards list for payer user
	cards, err := s.cards.GetSafeCardsByUserIdentifier(ctx, payer.Identifier)
	if err != nil {
		return nil, err
	}

	cardNumbers := make([]string, len(cards))
	for i, card := range cards {
		cardNumbers[i] = card.CardNumber
	}

	return &pb.FetchUserCardsListForDraftTransactionResponse{
		Error:           false,
		Message:         "cards fetched successfully",
		CardNumbersList: cardNumbers,
		Count:           int32(len(cards)),
	}, nil
}

// DraftTransaction implements pb.TransactionServiceServer.
func (s *service) DraftTransaction(ctx context.Context, req *pb.DraftTransactionRequest) (*pb.DraftTransactionResponse, error) {
	// find card info from card service mesh based on card number
	card, err := s.cards.GetSafeCardByCardNumber(ctx, req.CardNumber)
	if err != nil {
		return nil, err
	}

	// find user info from user service mesh based on payer identifier
	payer, err := s.users.GetSafeUserByIdentifier(ctx, req.PayerIdentifier)
	if err != nil {
		return nil, err
	}

	// marshal metadata to store in database JSONB
	metadata, err := json.Marshal(req.Metadata)
	if err != nil {
		return nil, err
	}

	accountAddress := fmt.Sprintf(s.config.DefaultCardCreditAccountPattern,
		card.UserIdentifier,
		card.IinCode,
		card.CardTypeCode,
		card.AccountNumber,
		card.LuhnDigit,
	)

	// define params to upsert transaction draft
	params := postgres.UpsertTransactionParams{
		Identifier:         ulid.Make().String(),
		DraftType:          postgres.TransactionDraftType(req.DraftType),
		ReferenceID:        req.ReferenceId,
		MetaData:           metadata,
		CardNumber:         card.CardNumber,
		DestinationAccount: req.DestinationAccount,
		SourceAccount:      accountAddress,
		Asset:              req.Asset,
		Amount:             req.Amount,
		ExpiresAt:          pgtype.Timestamptz{Time: time.Now().Add(time.Hour)},
	}

	// generate totp code and define its expire date
	totpGenCtx := context.Background()
	expire := time.Now().UTC().Add(time.Duration(s.config.VerificationDuration) * time.Minute).UTC()

	// write raw totp code to notification payload
	totp, err := s.totp_service.GenerateTOTP(totpGenCtx, req.PayerIdentifier, time.Duration(s.config.VerificationDuration)*time.Minute)
	if err != nil {
		return nil, err
	}

	// encrypt totp code to store in database
	encryptedTOTP, err := bcrypt.GenerateFromPassword([]byte(totp), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// set totp and expire date to params
	params.ExpiresAt = pgtype.Timestamptz{Time: expire, Valid: true}
	params.Totp = string(encryptedTOTP)

	// upsert transaction draft
	draft, err := s.repo.UpsertTransaction(ctx, params)
	if err != nil {
		return nil, err
	}

	contact, err := s.users.GetSafeUserByID(ctx, payer.ID)
	if err != nil {
		return nil, err
	}

	// generate SMS notification payload to send verification code for card owner'is mobile number
	payload := &worker.PayloadSendVerificationCode{}
	payload.Mobile = contact.Contact.Mobile
	payload.VerificationCode = totp
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	// define subject for notification
	// @ToDo: add notification topic pattern to notification service docs
	// example: service.notification.v1.mobile.SUPER_ADMIN
	// example: service.notification.v1.email.USER
	subject := fmt.Sprintf("%s.mobile.transaction", s.config.NotificationTopic)

	// publish notification payload to NATS
	err = s.nats.Publish(subject, jsonPayload)
	if err != nil {
		return nil, err
	}

	return &pb.DraftTransactionResponse{
		Error:   false,
		Message: "draft transaction created successfully",
		DraftTransaction: &pb.DraftTransaction{
			Identifier:  draft.Identifier,
			DraftType:   string(draft.DraftType),
			Totp:        totp,
			ReferenceId: draft.ReferenceID,
			Metadata:    req.Metadata,
			ExpiresAt:   draft.ExpiresAt.Time.String(),
			UpdatedAt:   draft.UpdatedAt.String(),
			CreatedAt:   draft.CreatedAt.String(),
		},
	}, nil

}
