package transaction

import (
	"sort"
	"strings"
)

// SortWorkflowsBySpecificity ranks workflows by how many literal (non-*) segments they match.
// This makes routing more deterministic.
func SortWorkflowsBySpecificity(workflows []PreloadedWorkflow) []PreloadedWorkflow {
	sort.Slice(workflows, func(i, j int) bool {
		count := func(pat string) int {
			segments := strings.Split(pat, ":")
			score := 0
			for _, segment := range segments {
				if segment != "*" {
					score += 1
				}
			}
			return score
		}
		si := count(workflows[i].SourceAddressPattern) + count(workflows[i].DestinationAddressPattern)
		sj := count(workflows[j].SourceAddressPattern) + count(workflows[j].DestinationAddressPattern)
		return si > sj // more specificity = higher rank
	})
	return workflows
}
