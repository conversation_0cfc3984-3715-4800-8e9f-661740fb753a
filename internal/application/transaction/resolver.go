package transaction

import "sync"

// matchSegmented compares a pattern with an address segment-by-segment.
// Wildcards match anything. It returns score and match status via channels.
func matchSegmented(pattern, address []string, scoreCh chan int32, okCh chan bool) {
	var score int32 = 0
	for i := range pattern {
		if pattern[i] == "*" {
			continue
		}
		if pattern[i] == address[i] {
			score++
		} else {
			okCh <- false
			scoreCh <- 0
			return
		}
	}
	okCh <- true
	scoreCh <- score
}

// computeScoreParallel runs source and destination matchers in parallel
// using goroutines and channels, combining their scores with any priority override.
func computeScoreParallel(workflow PreloadedWorkflow, srcAddr, dstAddr []string) (int32, bool) {
	srcChan := make(chan int32, 1)
	dstChan := make(chan int32, 1)
	okSrc := make(chan bool, 1)
	okDst := make(chan bool, 1)

	go matchSegmented(workflow.SrcSegments, srcAddr, srcChan, okSrc)
	go matchSegmented(workflow.DstSegments, dstAddr, dst<PERSON>han, okDst)

	srcOK := <-okSrc
	dstOK := <-okDst
	srcScore := <-srcChan
	dstScore := <-dstChan

	if !srcOK || !dstOK {
		return 0, false
	}

	var score int32 = srcScore + dstScore
	if workflow.PriorityOverride != DefaultWorkflowPriority {
		score += workflow.PriorityOverride
	}

	return score, true
}

// FindBestWorkflowParallel finds the most accurate workflow
// by evaluating all candidates in parallel using goroutines.
func FindBestWorkflowParallel(src, dst string, workflows []PreloadedWorkflow) *PreloadedWorkflow {
	srcParts := splitAddress(src)
	dstParts := splitAddress(dst)

	var best *PreloadedWorkflow
	highest := int32(-1)
	var mutex sync.Mutex
	var wait sync.WaitGroup

	for i := range workflows {
		wait.Add(1)
		go func(workflow PreloadedWorkflow) {
			defer wait.Done()
			score, matched := computeScoreParallel(workflow, srcParts, dstParts)
			if matched {
				mutex.Lock()
				if score > highest {
					highest = score
					best = &workflow
				}
				mutex.Unlock()
			}
		}(workflows[i])
	}
	wait.Wait()
	return best
}
