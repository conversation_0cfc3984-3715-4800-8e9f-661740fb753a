package domain

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

// AccountAddress is the account address type.
// It is used to identify account in the system.
// The format and segments is based on below format:
// user_identifier:iin:card_type:account_number:Domain[pre_paid:discount:...]:Asset.
type AccountAddress string

// IsValid checks if the account address is valid
func (a AccountAddress) IsValid() bool {
	regex := `^[a-zA-Z0-9_]+:[0-9]+:[0-9]+:[a-zA-Z0-9]+:([a-z][a-z_]*[a-z]:)*[a-z][a-z_]*[a-z]:[a-zA-Z0-9]+$`
	return regexp.MustCompile(regex).MatchString(a.GetString())
}

// GetString returns the string representation of the account address
func (a AccountAddress) GetString() string {
	return string(a)
}

// GetUserIdentifier returns the user identifier from account address
func (a AccountAddress) GetUserIdentifier() (string, error) {
	segments := strings.Split(a.GetString(), ":")
	if len(segments) < 6 {
		return "", fmt.Errorf("invalid account address: %s", a.GetString())
	}
	return segments[0], nil
}

// GetIIN returns the IIN from account address
func (a AccountAddress) GetIIN() (int, error) {
	segments := strings.Split(a.GetString(), ":")
	if len(segments) < 6 {
		return 0, fmt.Errorf("invalid account address: %s", a.GetString())
	}
	return strconv.Atoi(segments[1])
}

// GetCardType returns the card type from account address
func (a AccountAddress) GetCardType() (int, error) {
	segments := strings.Split(a.GetString(), ":")
	if len(segments) < 6 {
		return 0, fmt.Errorf("invalid account address: %s", a.GetString())
	}
	return strconv.Atoi(segments[2])
}

// GetAccountNumber returns the account number from account address
func (a AccountAddress) GetAccountNumber() (string, error) {
	segments := strings.Split(a.GetString(), ":")
	if len(segments) < 6 {
		return "", fmt.Errorf("invalid account address: %s", a.GetString())
	}
	return segments[3], nil
}

// GetDomain returns the domain from account address
func (a AccountAddress) GetDomain() (string, error) {
	segments := strings.Split(a.GetString(), ":")
	if len(segments) < 6 {
		return "", fmt.Errorf("invalid account address: %s", a.GetString())
	}
	return strings.Join(segments[3:len(segments)-1], ":"), nil
}

// GetAsset returns the asset from account address
func (a AccountAddress) GetAsset() (string, error) {
	segments := strings.Split(a.GetString(), ":")
	if len(segments) < 6 {
		return "", fmt.Errorf("invalid account address: %s", a.GetString())
	}
	return segments[len(segments)-1], nil
}

// Account is the account model
type Account struct {
	// account address
	Address AccountAddress `json:"address"`
	// account asset type
	Asset string `json:"asset"`
	// user identifier for account
	UserIdentifier string `json:"user_identifier"`
	// Issuer Identifier Number
	IIN int `json:"iin"`
	// Card type identifier number
	CardType int `json:"card_type"`
	// Account Number
	AccountNumber string `json:"account_number"`
	// account domain
	Domain string `json:"domain"`
}

// GenerateAddress generates account address based on account properties.
func (a *Account) GenerateAddress() string {
	return fmt.Sprintf("%s:%d:%d:%s:%s:%s", a.UserIdentifier, a.IIN, a.CardType, a.AccountNumber, a.Domain, a.Asset)
}
