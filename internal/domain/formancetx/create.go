package formancetx

import (
	"context"

	formancesdkgo "github.com/formancehq/formance-sdk-go/v3"
	"github.com/formancehq/formance-sdk-go/v3/pkg/models/operations"
	"github.com/formancehq/formance-sdk-go/v3/pkg/models/shared"
)

type Transaction struct {
	Postings  []shared.V2Posting
	Reference *string
	Script    *shared.V2PostTransactionScript
}

func CreateTransaction(ctx context.Context, formance *formancesdkgo.Formance, tx operations.V2CreateTransactionRequest) (*operations.V2CreateTransactionResponse, error) {
	return formance.Ledger.V2.CreateTransaction(ctx, tx)
}
