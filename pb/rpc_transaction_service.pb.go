// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_transaction_service.proto

package pb

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_rpc_transaction_service_proto protoreflect.FileDescriptor

const file_rpc_transaction_service_proto_rawDesc = "" +
	"\n" +
	"\x1drpc_transaction_service.proto\x12\x02pb\x1a\x1cgoogle/api/annotations.proto\x1a.protoc-gen-openapiv2/options/annotations.proto\x1a\rrpc_dto.proto2\x9c\x04\n" +
	"\x12TransactionService\x12r\n" +
	"\x10DraftTransaction\x12\x1b.pb.DraftTransactionRequest\x1a\x1c.pb.DraftTransactionResponse\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/v1/transaction/draft/draft\x12\x85\x01\n" +
	"\x16CommitDraftTransaction\x12!.pb.CommitDraftTransactionRequest\x1a\".pb.CommitDraftTransactionResponse\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/v1/transaction/draft/commit\x12p\n" +
	"\x11CommitTransaction\x12\x1c.pb.CommitTransactionRequest\x1a\x1d.pb.CommitTransactionResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/v1/transaction/commit\x12\x97\x01\n" +
	"!FetchUserCardsForDraftTransaction\x12\x1b.pb.DraftTransactionRequest\x1a1.pb.FetchUserCardsListForDraftTransactionResponse\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/v1/transaction/user/cardsB\xf0\x02\x92A\xc0\x02\x12\xac\x01\n" +
	"\x17Transaction Service API\x12RTransaction Service API for processing transaction request and related operations.\"6\n" +
	"\x1fTransaction Service API Support\x1a\x13liveutil@icloud.com2\x051.0.0*\x02\x01\x022\x10application/json:\x10application/jsonZY\n" +
	"W\n" +
	"\x06Bearer\x12M\b\x02\x128Authentication token, prefixed by Bearer: Bearer <token>\x1a\rAuthorization \x02b\f\n" +
	"\n" +
	"\n" +
	"\x06Bearer\x12\x00Z*github.com/liveutil/transaction_service/pbb\x06proto3"

var file_rpc_transaction_service_proto_goTypes = []any{
	(*DraftTransactionRequest)(nil),                       // 0: pb.DraftTransactionRequest
	(*CommitDraftTransactionRequest)(nil),                 // 1: pb.CommitDraftTransactionRequest
	(*CommitTransactionRequest)(nil),                      // 2: pb.CommitTransactionRequest
	(*DraftTransactionResponse)(nil),                      // 3: pb.DraftTransactionResponse
	(*CommitDraftTransactionResponse)(nil),                // 4: pb.CommitDraftTransactionResponse
	(*CommitTransactionResponse)(nil),                     // 5: pb.CommitTransactionResponse
	(*FetchUserCardsListForDraftTransactionResponse)(nil), // 6: pb.FetchUserCardsListForDraftTransactionResponse
}
var file_rpc_transaction_service_proto_depIdxs = []int32{
	0, // 0: pb.TransactionService.DraftTransaction:input_type -> pb.DraftTransactionRequest
	1, // 1: pb.TransactionService.CommitDraftTransaction:input_type -> pb.CommitDraftTransactionRequest
	2, // 2: pb.TransactionService.CommitTransaction:input_type -> pb.CommitTransactionRequest
	0, // 3: pb.TransactionService.FetchUserCardsForDraftTransaction:input_type -> pb.DraftTransactionRequest
	3, // 4: pb.TransactionService.DraftTransaction:output_type -> pb.DraftTransactionResponse
	4, // 5: pb.TransactionService.CommitDraftTransaction:output_type -> pb.CommitDraftTransactionResponse
	5, // 6: pb.TransactionService.CommitTransaction:output_type -> pb.CommitTransactionResponse
	6, // 7: pb.TransactionService.FetchUserCardsForDraftTransaction:output_type -> pb.FetchUserCardsListForDraftTransactionResponse
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rpc_transaction_service_proto_init() }
func file_rpc_transaction_service_proto_init() {
	if File_rpc_transaction_service_proto != nil {
		return
	}
	file_rpc_dto_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_transaction_service_proto_rawDesc), len(file_rpc_transaction_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_transaction_service_proto_goTypes,
		DependencyIndexes: file_rpc_transaction_service_proto_depIdxs,
	}.Build()
	File_rpc_transaction_service_proto = out.File
	file_rpc_transaction_service_proto_goTypes = nil
	file_rpc_transaction_service_proto_depIdxs = nil
}
