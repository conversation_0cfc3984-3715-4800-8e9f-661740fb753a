// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_draft_transaction.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DraftTransaction is the draft transaction object
type DraftTransaction struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// represent type of draft transaction
	DraftType string `protobuf:"bytes,2,opt,name=draft_type,json=draftType,proto3" json:"draft_type,omitempty"`
	// bcrypted TOTP code for validating transaction during committing it
	Totp string `protobuf:"bytes,3,opt,name=totp,proto3" json:"totp,omitempty"`
	// idempotency key for making create draft transaction idempotent
	ReferenceId string `protobuf:"bytes,4,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// transaction metadatas
	Metadata map[string]string `protobuf:"bytes,5,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// when the draft will expires
	ExpiresAt string `protobuf:"bytes,6,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	// when draft was updated
	UpdatedAt string `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// when draft was created
	CreatedAt     string `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DraftTransaction) Reset() {
	*x = DraftTransaction{}
	mi := &file_rpc_draft_transaction_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DraftTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DraftTransaction) ProtoMessage() {}

func (x *DraftTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_draft_transaction_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DraftTransaction.ProtoReflect.Descriptor instead.
func (*DraftTransaction) Descriptor() ([]byte, []int) {
	return file_rpc_draft_transaction_proto_rawDescGZIP(), []int{0}
}

func (x *DraftTransaction) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *DraftTransaction) GetDraftType() string {
	if x != nil {
		return x.DraftType
	}
	return ""
}

func (x *DraftTransaction) GetTotp() string {
	if x != nil {
		return x.Totp
	}
	return ""
}

func (x *DraftTransaction) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *DraftTransaction) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *DraftTransaction) GetExpiresAt() string {
	if x != nil {
		return x.ExpiresAt
	}
	return ""
}

func (x *DraftTransaction) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *DraftTransaction) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

var File_rpc_draft_transaction_proto protoreflect.FileDescriptor

const file_rpc_draft_transaction_proto_rawDesc = "" +
	"\n" +
	"\x1brpc_draft_transaction.proto\x12\x02pb\"\xe2\x02\n" +
	"\x10DraftTransaction\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12\x1d\n" +
	"\n" +
	"draft_type\x18\x02 \x01(\tR\tdraftType\x12\x12\n" +
	"\x04totp\x18\x03 \x01(\tR\x04totp\x12!\n" +
	"\freference_id\x18\x04 \x01(\tR\vreferenceId\x12>\n" +
	"\bmetadata\x18\x05 \x03(\v2\".pb.DraftTransaction.MetadataEntryR\bmetadata\x12\x1d\n" +
	"\n" +
	"expires_at\x18\x06 \x01(\tR\texpiresAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\a \x01(\tR\tupdatedAt\x12\x1d\n" +
	"\n" +
	"created_at\x18\b \x01(\tR\tcreatedAt\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B,Z*github.com/liveutil/transaction_service/pbb\x06proto3"

var (
	file_rpc_draft_transaction_proto_rawDescOnce sync.Once
	file_rpc_draft_transaction_proto_rawDescData []byte
)

func file_rpc_draft_transaction_proto_rawDescGZIP() []byte {
	file_rpc_draft_transaction_proto_rawDescOnce.Do(func() {
		file_rpc_draft_transaction_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_draft_transaction_proto_rawDesc), len(file_rpc_draft_transaction_proto_rawDesc)))
	})
	return file_rpc_draft_transaction_proto_rawDescData
}

var file_rpc_draft_transaction_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_rpc_draft_transaction_proto_goTypes = []any{
	(*DraftTransaction)(nil), // 0: pb.DraftTransaction
	nil,                      // 1: pb.DraftTransaction.MetadataEntry
}
var file_rpc_draft_transaction_proto_depIdxs = []int32{
	1, // 0: pb.DraftTransaction.metadata:type_name -> pb.DraftTransaction.MetadataEntry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_rpc_draft_transaction_proto_init() }
func file_rpc_draft_transaction_proto_init() {
	if File_rpc_draft_transaction_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_draft_transaction_proto_rawDesc), len(file_rpc_draft_transaction_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_draft_transaction_proto_goTypes,
		DependencyIndexes: file_rpc_draft_transaction_proto_depIdxs,
		MessageInfos:      file_rpc_draft_transaction_proto_msgTypes,
	}.Build()
	File_rpc_draft_transaction_proto = out.File
	file_rpc_draft_transaction_proto_goTypes = nil
	file_rpc_draft_transaction_proto_depIdxs = nil
}
