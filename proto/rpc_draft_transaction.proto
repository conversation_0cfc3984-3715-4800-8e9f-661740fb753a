syntax = "proto3";

package pb;

option go_package = "github.com/liveutil/transaction_service/pb";

// DraftTransaction is the draft transaction object
message DraftTransaction {
  // unique external identifier for internal-external system identifier isolation
  string identifier = 1;
  // represent type of draft transaction
  string draft_type = 2;
  // bcrypted TOTP code for validating transaction during committing it
  string totp = 3;
  // idempotency key for making create draft transaction idempotent
  string reference_id = 4;
  // transaction metadatas
  map<string, string> metadata = 5;
  // when the draft will expires
  string expires_at = 6;
  // when draft was updated
  string updated_at = 7;
  // when draft was created
  string created_at = 8;
}