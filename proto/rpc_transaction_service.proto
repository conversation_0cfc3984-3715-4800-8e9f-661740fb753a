syntax = "proto3";

package pb;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "rpc_dto.proto";

// option go_package = "./proto;transaction _service";
option go_package = "github.com/liveutil/transaction_service/pb";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Transaction Service API";
    version: "1.0.0";
    description: "Transaction Service API for processing transaction request and related operations.";
    contact: {
      name: "Transaction Service API Support";
      email: "<EMAIL>";
    };
  };
  schemes: HTTP;
  schemes: HTTPS;
  consumes: "application/json";
  produces: "application/json";
  security_definitions: {
    security: {
      key: "Bearer";
      value: {
        type: TYPE_API_KEY;
        in: IN_HEADER;
        name: "Authorization";
        description: "Authentication token, prefixed by Bearer: Bearer <token>";
      };
    };
  };
  security: {
    security_requirement: {
      key: "Bearer";
      value: {};
    };
  };
};

// Transaction Service API for processing transaction request and related operations.
service TransactionService {
  // DraftTransaction process request for storing temporary transaction with specified customer mobile/card no or national id
  rpc DraftTransaction(DraftTransactionRequest) returns (DraftTransactionResponse) {
    option (google.api.http) = {
      get: "/v1/transaction/draft/draft"
    };
  }

  // CommitDraftTransaction validate TOTP code that sent to customer's mobile number and commit transaction to Core Payment Service
  rpc CommitDraftTransaction(CommitDraftTransactionRequest) returns (CommitDraftTransactionResponse) {
    option (google.api.http) = {
      get: "/v1/transaction/draft/commit"
    };
  }

  // CommitTransaction commit transaction to Core Payment Service
  rpc CommitTransaction(CommitTransactionRequest) returns (CommitTransactionResponse) {
    option (google.api.http) = {
      get: "/v1/transaction/commit"
    };
  }

  // FetchUserCards fetch user cards for letting him to chose one for transaction
  rpc FetchUserCardsForDraftTransaction(DraftTransactionRequest) returns (FetchUserCardsListForDraftTransactionResponse) {
    option (google.api.http) = {
      get: "/v1/transaction/user/cards"
    };
  }
}