syntax = "proto3";

package pb;

import "rpc_draft_transaction.proto";

option go_package = "github.com/liveutil/transaction_service/pb";

// Transaction is the transaction object
message Transaction {
  // source_account is the source account address for payer of the payment
  string source_account = 1;
  // destination_account is the destination account address for acceptor of the payment
  string destination_account = 2;
  // reference_id is actually idempotency key for making create draft transaction idempotent
  string reference_id = 3;
  // transaction amount
  double amount = 4;
  // transaction asset type
  string asset = 5;
  // transaction description
  string description = 6;
  // tracing_id is the tracing id for transaction
  string tracing_id = 7;
  // transaction metadata
  map<string, string> metadata = 8;
}