services:
  postgres:
    image: postgres:16.1-alpine
    hostname: close-loop-postgres
    container_name: close-loop-postgres
    restart: always
    environment:
      - POSTGRES_USER=close_loop
      - POSTGRES_PASSWORD=close_loop_postgres_pass
      - POSTGRES_DB=close_loop_db
      - DATABASE_NAME=close_loop
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./internal/infrastructure/db/migrations/000001_init_schema.up.sql:/docker-entrypoint-initdb.d/01_init_schema.up.sql

  redis:
    image: redis:7-alpine
    hostname: close-loop-redis
    container_name: close-loop-redis
    depends_on:
      - postgres
    restart: always
    ports:
      - "6380:6379"
    command: redis-server --save 60 1 --loglevel warning --requirepass bBMUvLtS8KYzxuEXc0Mkj34c4Ph7eGOxMggCtictqSw0KP09
    volumes:
      - redis_data:/data

  nats:
    image: nats:2.10.11-alpine3.19
    container_name: close-loop-nats
    hostname: close-loop-nats
    restart: always
    ports:
      - "4222:4222"
      - "8222:8222"
    expose:
      - "4222"
      - "8222"
  
  mongodb:
    image: mongo:4.4.6
    container_name: close-loop-mongodb
    hostname: close-loop-mongodb
    restart: always
    ports:
      - "27017:27017"
    expose:
      - "27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: close_loop
      MONGO_INITDB_ROOT_PASSWORD: close_loop_logs_mongodb_pass
    volumes:
      - mongodb_data:/data
  
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "4318:4318"
    environment:
      COLLECTOR_ZIPKIN_HTTP_PORT: 9411

volumes:
  postgres_data:
  redis_data:
  mongodb_data:
